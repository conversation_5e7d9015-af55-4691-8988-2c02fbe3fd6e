import React, { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import type { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AllCommunityModule, ModuleRegistry } from 'ag-grid-community'; // Import all modules
import { useBomPdfExtractorStore } from "../BomPdfExtractorStore";
import styles from "../styles/BomExtractor.module.scss";
import { useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library";
import { parseSteel } from '@bryzos/steel-search-lib';
import { RowData } from "src/renderer2/pages/exporttToExcelUtils";
import { exportToExcel, formattedDate, urlToBase64NoPrefix } from "src/renderer2/helper";
import logo from "../../../../assets/New-images/logo.png"

const INITIAL_COLUMNS: ColDef[] = [];

import type { ICellRendererParams } from 'ag-grid-community';
import { Height } from "@mui/icons-material";
import NoSelectTextEditor from "./NoSelectTextEditor";
import { min } from "moment";
import DeleteCellRenderer from "./DeleteCellRenderer";
import clsx from "clsx";
import { useAvailableHeight } from "../utils/useAvailableHeight";

function MultilineRenderer(p: ICellRendererParams) {
  const text = String(p.value ?? '').replace(/\\n/g, '\n'); // turn "\n" into actual newlines
  return (
    <div style={{ whiteSpace: 'pre-wrap', lineHeight: 1.3, width: '100%' }}>
      {text}
    </div>
  );
}

const BOMExtractedDataGrid: React.FC = () => {
  const gridRef = useRef<AgGridReact>(null);
  const emptyTableRef = useRef<HTMLDivElement>(null);
  const [gridColumnDefs, setGridColumnDefs] = useState<ColDef[]>(INITIAL_COLUMNS);
  const [rowData, setRowData] = useState<any[]>([]);
  const [emptyRowCount, setEmptyRowCount] = useState<number>(18);
  const { columnDef, gridRowData, productSearcher, setFinalExtractedData, setDoSubmit } = useBomPdfExtractorStore();
  const { productData } = useGlobalStore();

  const handleDeleteRow = (row: any) => {
    setRowData(prev => prev.filter(r => r.id !== row.id));
  };

  // Calculate number of empty rows based on container height
  useEffect(() => {
    const calculateEmptyRows = () => {
      if (emptyTableRef.current && rowData.length === 0) {
        const containerHeight = emptyTableRef.current.clientHeight;
        const headerHeight = 61; // Approximate header height
        const rowHeight = 40; // Approximate row height based on CSS
        const padding = 40; // Container padding

        const availableHeight = containerHeight - headerHeight - padding;
        const calculatedRows = Math.floor(availableHeight / rowHeight);

        // Ensure minimum of 8 rows and maximum of 20 rows
        const finalRowCount = Math.max(8, Math.min(calculatedRows, 20));
        setEmptyRowCount(finalRowCount);
      }
    };

    calculateEmptyRows();

    // Recalculate on window resize
    const handleResize = () => calculateEmptyRows();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, [rowData.length]);

  useEffect(() => {
    setGridColumnDefs(prev => columnDef.map(item => ({ ...item, onCellValueChanged: onCellValueChanged, cellEditor: NoSelectTextEditor })));
  }, [columnDef]);

  const onCellValueChanged = (params) => {
    const searchCriteria = [
      params.data.description || '',
      params.data.length || '',
      params.data.specification || '',
      params.data.grade || ''
    ].filter(Boolean).join(' ');

    const splitObj = parseSteel(searchCriteria);
    const splitString = [
      splitObj.shape || '',
      splitObj.dims || '',
      splitObj.length || '',
      splitObj.grade || ''
    ].filter(Boolean).join(' ');
    if (!splitObj.shape && !splitObj.dims && !splitObj.length && !splitObj.grade) {
      // bad match set the productIds to empty array
      params.data.productIds = [];
      params.data.ProductDescription = '';
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
      return;
    }
    const newResult = productSearcher.search(splitObj, true);
    if (newResult?.results.length === 1) {
      params.data.productIds = [newResult.results[0].Product_ID];
      params.data.ProductDescription = getProductDescription(newResult.results[0].Product_ID);
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
    } else {
      params.data.productIds = [];
      params.data.ProductDescription = '';
      params.api.refreshCells({ rowNodes: [params.node], columns: ['ProductDescription'] });
    }
  }

  const getProductDescription = (id) => {
    if (!productData && !id) return '';
    const product = productData.find(item => item.Product_ID === id);
    if (product) {
      return product.UI_Description;
    }
    return ''
  }

  useEffect(() => {
    if (gridRowData.length === 0) {
      setRowData([])
      return;
    }
    const tempRowData = gridRowData.map((row) => {
      return { ...row, ProductDescription: row.productIds.length === 1 ? getProductDescription(row.productIds[0]) : '' };
    });
    setGridColumnDefs((prev) => {
      const temp = columnDef.map(item => ({ ...item, onCellValueChanged: onCellValueChanged, cellEditor: NoSelectTextEditor }))
      return [...temp, {
        field: "ProductDescription",
        headerName: "Matched Product", wrapText: true,      // allow wrapping
        autoHeight: true,    // grow row to fit
        minWidth: 300,
        cellRenderer: MultilineRenderer
      },
      {
        field: "delete",
        headerName: "Delete",
        autoHeight: true,    // grow row to fit
        minWidth: 300,
        cellRenderer: DeleteCellRenderer,
        cellRendererParams: {
            onDeleteRow: handleDeleteRow
        }
      }
    ];
    });
    setRowData([...tempRowData]);
  }, [gridRowData]);

  const chars = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

  function localTimestamp(ts = Date.now()) {
    const d = new Date(ts);
    const date = new Intl.DateTimeFormat('en-US', {
      month: 'long', day: 'numeric', year: 'numeric'
    }).format(d);
    const parts = new Intl.DateTimeFormat('en-US', {
      hour: 'numeric', minute: '2-digit', hour12: true
    }).formatToParts(d);
    const h = parts.find(p => p.type === 'hour').value;
    const m = parts.find(p => p.type === 'minute').value;
    const ampm = parts.find(p => p.type === 'dayPeriod').value.toLowerCase().replace(/\./g, '');
    return `${date} at ${h}:${m}${ampm}`;
  }

  const handleExportToExcel = async () => {
    // const rowData: any[] = [];
    // gridRef.current?.api.forEachNode(({data}) => {
    //   const temp = gridColumnDefs.reduce((acc, col) => {
    //       acc[col.field] = data[col.field];
    //       return acc;
    //     }, {})
    //     rowData.push(temp);
    // });
    // const worksheet = XLSX.utils.json_to_sheet(rowData);
    // const workbook = XLSX.utils.book_new();
    // XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    // XLSX.writeFile(workbook, "grid-data.xlsx");

    const { uploadBomInitialData } = useCreatePoStore.getState();
    const rowData: RowData[] = [];
    const headerRow: RowData = {
      row: [],
      style: {
        b: true, backgroundColor: 'FF000000', color: { argb: 'FFFFFFFF' },
        alignment: { wrapText: true, vertical: 'middle', horizontal: 'center' },
        font: { size: 14 },
        border: {
          top: { style: 'thin' },
          left: { style: 'thin' },
          right: { style: 'thin' },
          bottom: { style: 'thick' },
        }
      },
      height: 50
    };

    let headerRowCount = 0;
    gridColumnDefs.forEach(item => {
      if (item.field === 'delete') return;
      if (item.field === 'ProductDescription')
        headerRow.row.push({ cellText: item.headerName?.toUpperCase() });
      else
        headerRow.row.push({ cellText: "INPUT\n" + item.headerName?.toUpperCase(), style: { backgroundColor: 'FF595959' } });
      headerRowCount++;
    });

    rowData.push({
      row: [{ cellText: ' ' }],
    });
    rowData.push({
      row: [{ cellText: ' ' }],
    });
    rowData.push({
      row: [{ cellText: ' ' }],
    });
    rowData.push({
      row: [{ cellText: ' ' }],
    });
    rowData.push({
      row: [{ cellText: `This bill of material was exported from your desktop Bryzos application on Monday, ${localTimestamp()}.` }],
    });
    rowData.push({
      row: [{ cellText: `Name of Bill of Material: ${uploadBomInitialData.buyer_internal_po}` }],
    });



    rowData.push(headerRow);
    const filterRow = rowData.length;
    gridRef.current?.api.forEachNode(({ data }, index) => {
      const temp = gridColumnDefs.reduce((acc, col) => {
        if (col.field === 'delete') return acc;
        if (col.field === 'ProductDescription')
          acc.push({ cellText: data[col.field], style: { alignment: { wrapText: true, vertical: 'middle', horizontal: 'left' } } });
        else
          acc.push({ cellText: data[col.field], style: { alignment: { wrapText: true, vertical: 'middle', horizontal: 'center' } } });
        return acc;
      }, [])
      rowData.push({
        row: temp, style: { border: true, backgroundColor: index % 2 === 0 ? 'FFD9D9D9' : 'FFFFFFFF', }
      });
    });

    const filterRange = `${chars[0]}${filterRow}:${chars[headerRowCount - 1]}${filterRow}`;
    console.log(chars[headerRowCount - 1])
    const MY_LOGO_BASE64_NO_PREFIX = await urlToBase64NoPrefix(logo);

    const resizeColumns = [];
    for (let i = 1; i < headerRowCount; i++) {
      resizeColumns.push(chars[i]);
    }

    exportToExcel(rowData, `${uploadBomInitialData.buyer_internal_po} BOMExtractedData ${formattedDate}`, {
      sheetName: 'Report',
      freeze: { rows: 7 },
      resizeColumns: resizeColumns,                     // keep title + header in view 
      columnWidths: [20],
      autoFilter: filterRange,                     // filter on header row
      showGridLines: false,
      logo: {
        base64: MY_LOGO_BASE64_NO_PREFIX,      // e.g., 'iVBORw0KGgoAAA...'
        extension: 'png',
        tl: { col: 1, row: 1 },                // near A1 (under merged title)
        ext: { width: 280, height: 80 },
      },
    });
  };

  const getPricingPage = () => {
    setFinalExtractedData(prev => rowData.map(item => ({ ...item })));
    setDoSubmit(true);
  };

  const onFirstDataRendered = (params) => {
    //params.api.setFocusedCell(0, gridColumnDefs[0].field)
    const firstCol = gridColumnDefs[0].field;

    params.api.startEditingCell({ rowIndex: 0, colKey: firstCol });

    // Wait a tick so the editor mounts, then place caret (no selection)
    setTimeout(() => {
      const [editor] = params.api.getCellEditorInstances({ rowIndex: 0, column: firstCol }) || [];
      if (!editor || !editor.getGui) return;
      const el = editor.getGui().querySelector<HTMLInputElement | HTMLTextAreaElement>('input,textarea');
      if (!el) return;
      const end = el.value?.length ?? 0;
      el.focus();
      el.setSelectionRange(end, end); // caret at end, zero-length selection
    }, 0);
  }

  const getTableHeader = () => { 
    const minHeaderCount = 5;
    const dummyHeader = [...gridColumnDefs];
    while(dummyHeader.length < minHeaderCount){
      dummyHeader.push({ field: `empty${dummyHeader.length}`, headerName: '' });
    }

    return (<tr>
      {dummyHeader.map(item => (
        <th key={item.field} style={{ color: 'white', minWidth: 100 }} className={styles.emptyTableHeader}>{item.headerName}</th>
      ))}
    </tr>)


  }
  const gridContainer = useRef<HTMLDivElement>(null);
 useAvailableHeight(gridContainer);

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <div className={styles.agGridHeader}><span className={styles.noteText}>Edit extracted data as needed in each cell.</span><button disabled={!rowData || rowData.length === 0} className={styles.btnExport} onClick={handleExportToExcel}>Export</button></div>
      <div ref={gridContainer} className={clsx("ag-theme-alpine", styles.agGridTableContainer)} style={{ width: "100%" }}>
        {rowData.length > 0 ? (
          <AgGridReact
            ref={gridRef}
            columnDefs={gridColumnDefs}
            defaultColDef={{ resizable: true, singleClickEdit: true, suppressMovable: true }}
            suppressMovableColumns={true}
            suppressDragLeaveHidesColumns={true}
            alwaysShowHorizontalScroll={true}
            rowData={rowData}
            headerHeight={61}
            rowHeight={100}
            onFirstDataRendered={onFirstDataRendered}
            getRowStyle={(params) => {
              return {
                backgroundColor: params.node.rowIndex % 2 === 0 ? '#fff' : '#f2f4f6',
              };
            }}
          />
        ) : (
          <div ref={emptyTableRef} className={styles.emptyTableContainer} style={{ height: '100%' }}>
            <table className={styles.emptyTable}>
              <thead>
                {getTableHeader()}
              </thead>
              <tbody>
                {[...Array(emptyRowCount)].map((_, index) => (
                  <tr key={index} className={styles.emptyTableRow}>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                    <td className={styles.emptyTableCell}></td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      <button disabled={rowData.length === 0} className={styles.btnGetPricing} onClick={getPricingPage}>GET PRICING</button>
    </div>
  );
};

export default BOMExtractedDataGrid;

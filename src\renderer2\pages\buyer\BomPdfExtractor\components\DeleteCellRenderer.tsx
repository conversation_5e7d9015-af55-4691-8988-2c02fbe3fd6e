// DeleteCellRenderer.tsx
import React from "react";
import type { ICellRendererParams } from "ag-grid-community";

type DeleteCellRendererProps = ICellRendererParams & {
    onDeleteRow: (row: any) => void;
  };

export default function DeleteCellRenderer(props: DeleteCellRendererProps) {
    const handleDelete = () => {
        // Remove from grid view
        props.api.applyTransaction({ remove: [props.node.data] });
        // Remove from source data in parent
        props.onDeleteRow(props.node.data);
      };

  return (
    <button
      type="button"
      onClick={handleDelete}
      aria-label="Delete row"
      style={{
        padding: "4px 10px",
        borderRadius: 6,
        border: "1px solid #e11d48",
        background: "#fee2e2",
        cursor: "pointer",
      }}
    >
      Delete
    </button>
  );
}

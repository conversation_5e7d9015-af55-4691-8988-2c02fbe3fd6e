import React, { useEffect, useState } from 'react'
import styles from './AppUpdateAvailablePopup.module.scss'
import { getChannelWindow, useGlobalStore } from '@bryzos/giss-ui-library'
import { Dialog } from '@mui/material'

const AppUpdateAvailablePopup = ( {fullWidthContainerRef}: {fullWidthContainerRef: React.RefObject<HTMLDivElement>} ) => {
  const [releaseNotes, setReleaseNotes] = useState('');
  const [currentVersion, setCurrentVersion] = useState('');
  const [releaseVersion, setReleaseVersion] = useState('');
  const {openAutoUpdatePopup, setOpenAutoUpdatePopup} = useGlobalStore();
  const channelWindow =  getChannelWindow() ;
  
  useEffect(()=>{
    if(channelWindow?.updateAvailable){
      window.electron.receive(channelWindow.updateAvailable, (data)=>{
        if(data){
          setReleaseNotes(data.releaseNotes);
          setCurrentVersion(data.currentVersion);
          setReleaseVersion((data.releaseName.charAt(0).toLowerCase() === 'v')?data.releaseName.slice(1):data.releaseName);
          setOpenAutoUpdatePopup(true);
        }
      })
    }
  },[]);
 
  const handleClose = () => {
    if(channelWindow?.closeNewUpdateWindow) {
      setOpenAutoUpdatePopup(false);
      setReleaseNotes('');
      setCurrentVersion('');
      setReleaseVersion('');
    }
  };

  const handleUpdate = () => {
    if(channelWindow?.doUpdate){
      window.electron.send({ channel: channelWindow.doUpdate }); 
      setTimeout(()=>{
        handleClose();
      }, 300)
    }
  };

  if(!openAutoUpdatePopup) return null;
  
  
  return (
    <Dialog
    open={openAutoUpdatePopup && !!releaseVersion}
    transitionDuration={100}
    disableScrollLock={true}
    container={fullWidthContainerRef.current}
    style={{
      position: 'absolute',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backdropFilter: 'blur(7px)', 
      background: 'rgba(0, 0, 0, 1)', // Fully opaque black
      WebkitBackdropFilter: 'blur(7px)',
      // backgroundColor: 'rgba(0, 0, 0, 0)',
      border: '1px solid transparent',
      zIndex:999999
    }}
    PaperProps={{
      style: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        margin: 0,
        width: '100%',
        maxWidth: '980px',
        minWidth: '761px',
        borderRadius: '16px',
        boxShadow: '0 0 67.4px 4px #000',
        backgroundColor: 'black',
      }
    }}
    hideBackdrop
  >
    <div className={styles.modal}>
      {/* Close Button */}
      <button className={styles.closeButton} onClick={handleClose}>
        ✕
      </button>

      {/* Title */}
      <h2 className={styles.title}>NEW UPDATE AVAILABLE</h2>

      {/* Version Information */}
      <div className={styles.versionInfo}>
        <p>New version {releaseVersion} is now available.</p>
        <p>You are currently using {currentVersion}.</p>
      </div>

      {/* Update Contents */}
      <div className={styles.updateContents}>
        <div>This update includes:</div>
        <div 
          className="releaseNotesContent"
          dangerouslySetInnerHTML={{ __html: releaseNotes }} 
          style={{color: 'white'}}
        />
      </div>

      {/* Action Buttons */}
      <div className={styles.actionButtons}>
        <button className={styles.keepCurrentButton} onClick={handleClose}>
          KEEP CURRENT
        </button>
        <button className={styles.updateAppButton} onClick={handleUpdate}>
          UPDATE APP
        </button>
      </div>
    </div>
  </Dialog>
  )
}

export default AppUpdateAvailablePopup

import { commomKeys, format4DigitAmount, formatToTwoDecimalPlaces, getFloatRemainder, getFormattedUnit, getValUsingUnitKey, newPricingPrefix, noIdGeneric, orderIncrementPrefix, orderType, priceUnits, useBuyerSettingStore, useCalculateDeposit, useCalculateSalesTax, useCreatePoStore, useGlobalStore, useOrderManagementStore, usePostCalculateOrderCharges, useSaveProductSearchAnaytic, useSaveUserActivity, useStateZipValidation } from "@bryzos/giss-ui-library";
import { useEffect, useState } from "react";
import { getPriceExample } from "../utility/priceIntegratorExample";
import { calculateTotalWeightForProduct, descriptionLines, getOtherDescriptionLines } from "../utility/pdfUtils";
import { useDebouncedValue } from "@mantine/hooks";
import { localStorageKeys, routes } from "../common";
import { useLocation } from "react-router-dom";
import { clearLocal } from "../helper";
import useDialogStore from "../component/DialogPopup/DialogStore";
import useGetDraftLines from "./useGetDraftLines";
import { useBomPdfExtractorStore } from "../pages/buyer/BomPdfExtractor/BomPdfExtractorStore";
import usePostFetchPoPrices from "./usePostFetchPoPrices";

const useCreatePoPriceCalculation = (setValue: any, clearErrors: any, userPartData: any, getValues: any, remove: any, sessionId: string, watch: any, trigger: any, setError: any, initialData: any, setFormErrors: any, formErrors: any, createPoResultCopy: any, pageIndex: any, itemsToAddOnScroll: any, handleFilterFieldsData: any, setCreatePoResultCopy: any, poHeaderFormWatch: any, bnplCredit: any, setSelectedProduct: any, updateLocalStoreQuote: any) => {
    const location = useLocation();
    const [salesTaxCounter, setSalesTaxCounter] = useState(0);
    // const checkStateZipValidation = useStateZipValidation();
    const [debouncedTotalWeight] = useDebouncedValue(watch('total_weight'), 400);
    const [debouncedMatTotal] = useDebouncedValue(watch('buyer_po_price'), 400);
    const [handleSubmitValidation, setHandleSubmitValidation] = useState(false);

    const saveProductSearch = useSaveProductSearchAnaytic();
    const { mutate: logUserActivity } = useSaveUserActivity();
    // const calculateSalesTax = useCalculateSalesTax();
    const calculateDepositTax = useCalculateDeposit();
    const calculateOrderCharges = usePostCalculateOrderCharges();
    const isSavedBom = location.pathname === routes.savedBom;
    const createPoDataFromSavedBom = useCreatePoStore(state => state.createPoDataFromSavedBom);
    // const isCreatePOModule = useCreatePoStore(state => state.isCreatePOModule);
    const weightGear = useCreatePoStore(state => state.weightGear);
    const createPoResultCopyList = Object.values(createPoResultCopy || {}) || [];
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const isCreatePoDirty = useCreatePoStore(state => state.isCreatePoDirty);
    // const draftOrderListFromSocket = useCreatePoStore((state: any) => state.draftOrderListFromSocket);
    // const setDraftOrderListFromSocket = useCreatePoStore((state: any) => state.setDraftOrderListFromSocket);
    // const updatedDraftId = useCreatePoStore((state: any) => state.updatedDraftId);
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const setSelectedQuote = useCreatePoStore((state: any) => state.setSelectedQuote);
    const setCreatePoData = useCreatePoStore((state: any) => state.setCreatePoData);
    const setShowLoader = useGlobalStore((state: any) => state.setShowLoader);
    const setIsCreatePoDirty = useCreatePoStore((state: any) => state.setIsCreatePoDirty);
    const productMapping = useGlobalStore((state: any) => state.productMapping);
    const isConvertingToPo = useCreatePoStore((state: any) => state.isConvertingToPo);
    const setIsConvertingToPo = useCreatePoStore((state: any) => state.setIsConvertingToPo);
    const {mutateAsync: getDraftLines} = useGetDraftLines();
    const draftDeletedSocketData = useCreatePoStore((state: any) => state.draftDeletedSocketData);
    const setDraftDeletedSocketData = useCreatePoStore((state: any) => state.setDraftDeletedSocketData);
    const showBackToBomUploadButton = useBomPdfExtractorStore((state: any) => state.showBackToBomUploadButton);
    const postFetchPoPrices = usePostFetchPoPrices(true);
    const isEditingPo = useOrderManagementStore(state => state.isEditingPo);

    useEffect(() => {
        handleChangePriceOnZipAndStateChange();
    }, [poHeaderFormWatch?.('shipping_details.zip'), poHeaderFormWatch?.('shipping_details.state_id')])

    const handleChangePriceOnZipAndStateChange = async() => {
        if ((location.pathname !== routes.orderManagementPage || (location.pathname === routes.orderManagementPage && isEditingPo)) && (createPoResultCopyList?.length > 0 && poHeaderFormWatch?.('shipping_details.zip')?.length === 5 && poHeaderFormWatch?.('shipping_details.state_id'))) {
            setIsCreatePoDirty(true);
            const isGearUpdated = await handlePriceIntegration();
            console.log("isGearUpdated ", isGearUpdated, createPoResultCopyList, " and createPoResultCopyList length ", createPoResultCopyList?.length, createPoResultCopyList[0]);
            handleFilterFieldsData();
        }
    }

    // useEffect(()=>{
    //     updateQuoteDetails();
    // },[debouncedTotalWeight, watch('totalPurchase')])

    // const updateQuoteDetails = () => {
    //     const localQuote = getLocal(localStorageKeys.poQuoting, null);
    //     if(localQuote && selectedQuote){
    //         const _localQuote = {...localQuote, ...(watch() as any)};
    //         setLocal(localStorageKeys.poQuoting, _localQuote);
    //         const _quoteList = quoteList.map((quote: any) => {
    //             if(quote.id === localQuote.id){
    //                 return _localQuote;
    //             }
    //             return quote;
    //         });
    //         setQuoteList(_quoteList);
    //     }
    // }


    useEffect(() => {
        if (watch('isEdit')) {
            setHandleSubmitValidation(false);
            paymentMethodChangeHandler().then(() => {
                calculateTotalPrice();
                setHandleSubmitValidation(true);
            });
        }
    }, [watch('payment_method')]);

    useEffect(() => {
        if (watch('isEdit')) {
            if (parseFloat(getValues('buyer_po_price')) && poHeaderFormWatch?.('shipping_details.zip')?.length === 5 && poHeaderFormWatch?.('shipping_details.state_id')) {
                setHandleSubmitValidation(false);
                calculateTotalPricing().then(() => {
                    setHandleSubmitValidation(true);
                });
            }
            else {
                setValue("sales_tax", parseFloat(0));
                setValue("deposit_amount", parseFloat(0));
            }
        }
    }, [debouncedMatTotal, poHeaderFormWatch?.('shipping_details.zip'), poHeaderFormWatch?.('shipping_details.state_id')])




    useEffect(() => {
        if (!(isSavedBom && createPoDataFromSavedBom && !watch('is_draft_po'))) {
            calculateTotalPrice();
            // if (!watch('is_draft_po')) {
            //     setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(createPoResultCopyList));
            // }
        }
    }, [watch('buyer_po_price'), watch('sales_tax'), watch('deposit_amount')]);

    useEffect(()=>{
        if(draftDeletedSocketData){
            if(selectedQuote?.id && draftDeletedSocketData?.id?.includes(selectedQuote?.id)){
                setSelectedQuote(null);
                const dialogText = selectedQuote?.order_type === orderType.QUOTE ? `The quote has been deleted.` : `The PO has been deleted.`;
                showCommonDialog(null, dialogText, commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            }
            setDraftDeletedSocketData(null);
        }
    },[draftDeletedSocketData])

    const updateLineProduct = (index: number, product: any) => {

        setSelectedProduct(product);

        setValue(`cart_items.${index}.qty`, '')
        setValue(`cart_items.${index}.buyer_line_total`, 0)
        setValue(`cart_items.${index}.seller_extended`, 0)
        setValue(`cart_items.${index}.buyer_price_per_unit`, 0)
        setValue(`cart_items.${index}.seller_price`, 0);
        setValue(`cart_items.${index}.price_unit`, '')
        setValue(`cart_items.${index}.qty_unit`, '')
        setValue(`cart_items.${index}.domesticMaterialOnly`, null)
        clearErrors(`cart_items.${index}.qty`)
        if (product) {
            const quantityOptions = product.QUM_Dropdown_Options.split(",");
            setValue(`cart_items.${index}.qty_um`, quantityOptions);
            setValue(`cart_items.${index}.qty_unit`, quantityOptions[0] || '');
            // resetQtyAndPricePerUnitFields(index, product);
            if (userPartData && Object.keys(userPartData)?.length) {
                setValue(`cart_items.${index}.product_tag`, userPartData[product?.Product_ID])
            }
        }
        else {
            setValue(`cart_items.${index}.descriptionObj`, {});
            handlePriceIntegration(undefined, Object.values(createPoResultCopy));
            // calculateMaterialTotalPrice();
        }
    }

    const calculateMaterialTotalPrice = () => {
        const items = createPoResultCopyList;
        const totalPurchase = items.reduce((accumaltaor: any, product: any) => {
            if (product && 'line_status' in product && product.line_status === "SKIPPED") return accumaltaor;
            const buyerLineTotal = (product.buyer_line_total && typeof product.buyer_line_total === 'string') ? parseFloat(product.buyer_line_total) : product.buyer_line_total ?? 0;
            accumaltaor.buyerTotalPurchase += buyerLineTotal;
            accumaltaor.sellerTotalPurchase += product.seller_extended ?? 0;
            return accumaltaor;
        }, { buyerTotalPurchase: 0, sellerTotalPurchase: 0 });
        setValue(`buyer_po_price`, String(totalPurchase.buyerTotalPurchase));
        setValue(`seller_price`, totalPurchase.sellerTotalPurchase.toFixed(2));
        if(isCreatePoDirty && poHeaderFormWatch?.()){
            updateLocalStoreQuote(items);
        }
    }

    const pricePerUnitChangeHandler = (index: any, product: any, priceData: any = 0) => {
        product = product ?? getValues(`cart_items.${index}.descriptionObj`);
        if (product && getValues(`cart_items.${index}.qty`) > 0) {
            const unit = getValues(`cart_items.${index}.price_unit`) || product.PUM_Dropdown_Options.split(",")[0];
            const umVal = getValUsingUnitKey(product, unit, newPricingPrefix);
            setValue(`cart_items.${index}.buyer_price_per_unit`, umVal);
            setValue(`cart_items.${index}.seller_price`, umVal);
        }
        else {
            setValue(`cart_items.${index}.buyer_price_per_unit`, 0);
            setValue(`cart_items.${index}.seller_price`, 0);
        }
    }

    const removeLineItem = (index: number, actualIndex: number) => {
        let newCreatePoResultCopy: any = {};
        Object.keys(createPoResultCopy).forEach((key: any) => {
            if(key < actualIndex) newCreatePoResultCopy[key] = {...createPoResultCopy[key]};
            if(key > actualIndex) newCreatePoResultCopy[key-1] = {...createPoResultCopy[key]};
            // if(key === actualIndex) createPoResultCopy[key] = {};
            // if (key > actualIndex) {
            //     createPoResultCopy[key-1] = {...createPoResultCopy[key]};
            //     delete createPoResultCopy[key];
            // }
        });
        setCreatePoResultCopy(newCreatePoResultCopy);
        // remove(index);
        // calculateMaterialTotalPrice();
        handleFilterFieldsData(pageIndex, newCreatePoResultCopy);
        const totalWeight = calculateTotalWeightForProduct(Object.values(newCreatePoResultCopy));
        setValue('total_weight', totalWeight);
        const isGearUpdated = willGearUpdate(totalWeight);
        if(isGearUpdated) 
            handlePriceIntegration(undefined, Object.values(newCreatePoResultCopy));
    }

    const handleCreatePOSearch = (searchStringKeyword: string, status: string, descriptionLineSessionId: string) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "keyword": searchStringKeyword,
                "status": status,
                "source": 'create-po',
                "line_session_id": descriptionLineSessionId,
            }
        }
        saveProductSearch.mutateAsync(payload)
            .catch(err => console.error(err))
    }


    const saveUserLineActivity = (sessionId: string, isRemovingLine: boolean = false, index: number) => {
        const { buyerSetting } = useBuyerSettingStore.getState();
        const defaultZipCode = buyerSetting?.price_search_zip || '63105';
        const zipcode = poHeaderFormWatch?.(`shipping_details.zip`)
        const _zipcode = zipcode?.length === 5 ? zipcode : defaultZipCode;
        const payload: any = {
            "data": {
                "session_id": sessionId ? sessionId : null,
                "po_line": index + 1,
                "line_session_id": getValues(`cart_items.${index}.line_session_id`) ? getValues(`cart_items.${index}.line_session_id`) : null,
                "in_po_line": !isRemovingLine,
                "description": getValues(`cart_items.${index}.description`) ? getValues(`cart_items.${index}.description`) : null,
                "qty": getValues(`cart_items.${index}.qty`) ? getValues(`cart_items.${index}.qty`) : null,
                "qty_unit": getValues(`cart_items.${index}.qty_unit`) ? getFormattedUnit(getValues(`cart_items.${index}.qty_unit`)) : null,
                "product_tag": getValues(`cart_items.${index}.product_tag`) ? getValues(`cart_items.${index}.product_tag`) : null,
                "price": getValues(`cart_items.${index}.buyer_price_per_unit`) ? getValues(`cart_items.${index}.buyer_price_per_unit`) : null,
                "price_unit": getValues(`cart_items.${index}.price_unit`) ? getFormattedUnit(getValues(`cart_items.${index}.price_unit`)) : null,
                "extended": getValues(`cart_items.${index}.buyer_line_total`) ? getValues(`cart_items.${index}.buyer_line_total`) : null,
                "product_id": getValues(`cart_items.${index}.product_id`) ? getValues(`cart_items.${index}.product_id`) : null,
                "reference_product_id": getValues(`cart_items.${index}.reference_product_id`) ? getValues(`cart_items.${index}.reference_product_id`) : null,
                "shape": getValues(`cart_items.${index}.shape`) ? getValues(`cart_items.${index}.shape`) : null,
                "domestic_material_only": getValues(`cart_items.${index}.domestic_material_only`),
                "order_size": getValues(`total_weight`) ? getValues(`total_weight`) : null,
                "zip_code": _zipcode ? _zipcode : null
            }
        }
        logUserActivity({ url: `${import.meta.env.VITE_API_SERVICE}/user/saveCreatePoLineData`, payload });
    }


    const handlePriceIntegration = async (index?: number, createPoList: any = createPoResultCopyList, _pageIndex: number = pageIndex) => {
        const totalWeight = calculateTotalWeightForProduct(createPoList);
        const isGearUpdated = willGearUpdate(totalWeight);
        setValue('total_weight', totalWeight);
        const indexToSet =  index && index - (_pageIndex * itemsToAddOnScroll);
        if (!isGearUpdated && indexToSet !== undefined) {
            console.log("weightGear is same as localWeightGear");
        } else {
            console.log("weightGear is not same as localWeightGear");
        }

        const productIdList = [];
        if(selectedQuote?.buyer_po_number || !(!isGearUpdated && indexToSet !== undefined)){
            for (const item of createPoList) {
                if (item?.descriptionObj?.Product_ID) {
                    productIdList.push(item.descriptionObj.Product_ID);
                }
            }
        } else {
            productIdList.push(watch(`cart_items.${indexToSet}.descriptionObj.Product_ID`));
        }
        const searchZipCode = poHeaderFormWatch?.(`shipping_details.zip`)
        if (productIdList.length === 0) return {};
        let netTotalWeight = totalWeight;
        if (netTotalWeight <= 500) {
            netTotalWeight = 500;
        }
        const productPricesData = await getPriceExample(productIdList, searchZipCode, Math.floor(netTotalWeight), selectedQuote?.buyer_po_number, postFetchPoPrices.mutateAsync);
        if (!isGearUpdated && indexToSet !== undefined) {
            updateValue(indexToSet, productPricesData)
            // saveUserLineActivity(sessionId, false, index);
        } else {
            updateAllValues(productPricesData, createPoList);
            // for (let i = 0; i < watch('cart_items').length; i++) {
            //     updateValue(i, productPricesData)
            //     if (watch(`cart_items.${i}.descriptionObj`)) {
            //         saveUserLineActivity(sessionId, false, i);
            //     }
            // }
        }
        return isGearUpdated;
    }



    const updateValue = async (index: number, priceMapping: any = {}) => {
        const actualIndex = index + (pageIndex * itemsToAddOnScroll);
        if (watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) {

            const _selected = getValues(`cart_items.${index}`).descriptionObj;
            const productId = watch(`cart_items.${index}.descriptionObj.Product_ID`)
            const priceUnit = getValues(`cart_items.${index}.price_unit`);

            if (!priceUnit || !watch(`cart_items.${index}.price_um`)) resetPricePerUnitFields(index, _selected)

            let selectedPriceUnit = watch(`cart_items.${index}.price_unit`)
            const prices = priceMapping[productId];

            const pumDropdownOptions = watch(`cart_items.${index}.price_um`);
            pumDropdownOptions.forEach((item: any) => {
                if (item.toLowerCase() !== "net ton" || item.toLowerCase() !== priceUnits.net_ton) {
                    setValue(`cart_items.${index}.descriptionObj.${newPricingPrefix}${item}`, prices?.[item] || prices?.[item.toLowerCase()])
                }
            })
            // setPriceData(result.price);
            if (_selected && Object.keys(_selected).length > 0) {
                const updateQtyUnit = watch(`cart_items.${index}.qty_unit`).toLowerCase();
                const updatePriceUnit = watch(`cart_items.${index}.price_unit`).toLowerCase();
                setValue(`cart_items.${index}.qty_unit`, updateQtyUnit);
                setValue(`cart_items.${index}.price_unit`, updatePriceUnit);
                updateLineItem(index, actualIndex);
                const qtyVal = watch(`cart_items.${index}.qty`);
                const qtyUnit = watch(`cart_items.${index}.qty_unit`);
                const priceUnit = watch(`cart_items.${index}.price_unit`);
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
                if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                    if (!priceUnit) resetPricePerUnitFields(index, _selected)
                } else {
                    if (qtyVal < 1) clearErrors(`cart_items.${index}.qty`);
                    setValue(`cart_items.${index}.price_unit`, '');
                    setValue(`cart_items.${index}.buyer_price_per_unit`, 0);
                    setValue(`cart_items.${index}.seller_price`, 0);
                }
            // calculateMaterialTotalPrice();
            }
        }else{
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [actualIndex]: {
                    qty: false,
                    product: true,
                    qtyEmpty: false
                }
            }));
        }
    }


    const resetPricePerUnitFields = (index: any, product: any) => {
        const priceUnitMData = product?.PUM_Dropdown_Options?.split(",").filter((item: any) => !(item.trim().toLowerCase() === priceUnits.net_ton || item.trim().toLowerCase() === 'net ton'));

        setValue(`cart_items.${index}.price_um`, priceUnitMData);
        if (!initialData?.cart_items?.[index]?.price_unit) {
            let priceUnit = priceUnitMData[0];
            if (initialData?.cart_items?.[index]?.bom_line_id && initialData?.cart_items?.[index]?.qty_unit) priceUnit = initialData?.cart_items?.[index]?.qty_unit;
            if(watch(`cart_items.${index}.bom_line_id`) && watch(`cart_items.${index}.qty_unit`)) priceUnit = watch(`cart_items.${index}.qty_unit`);
            setValue(`cart_items.${index}.price_unit`, priceUnit)
        } else {
            setValue(`cart_items.${index}.price_unit`, initialData?.cart_items?.[index]?.price_unit)
        }
        pricePerUnitChangeHandler(index, product);
    }


    const updateLineItem = (index: any, actualIndex: any) => {
        const _selected = getValues(`cart_items.${index}.descriptionObj`);

        if (watch(`cart_items.${index}.line_status`) === "SKIPPED") {
            return;
        }

        if (_selected) {
            const qtyVal = +getValues(`cart_items.${index}.qty`) || 0;
            const qtyUnit = getValues(`cart_items.${index}.qty_unit`);
            const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
            const updatedUnit = unit;
            const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
            pricePerUnitChangeHandler(index, undefined);
            if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                const buyerPricePerUnit = parseFloat(getValUsingUnitKey(_selected, unit, newPricingPrefix));
                setValue(`cart_items.${index}.buyer_calculation_price`, buyerPricePerUnit);
                setValue(`cart_items.${index}.seller_calculation_price`, buyerPricePerUnit);
                const totalVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                const extendedValue = buyerPricePerUnit * qtyVal;
                const totalSellerVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                setValue(`cart_items.${index}.buyer_line_total`, +totalVal);
                setValue(`cart_items.${index}.seller_extended`, +totalSellerVal);
                setValue(`cart_items.${index}.extendedValue`, +extendedValue);
                setFormErrors(prevErrors => {
                    const newErrors = { ...prevErrors };
                    delete newErrors[actualIndex];
                    return newErrors;
                });
                clearErrors(`cart_items.${index}.qty`);
                trigger(`cart_items.${index}.qty`);
            } else {
                if (_selected) setFormErrors(prevErrors => ({
                    ...prevErrors,
                    [actualIndex]: {
                        qty: true,
                        product: false,
                        qtyEmpty: false
                    }
                }));
                if (qtyVal === null) setValue(`cart_items.${index}.qty`, '');
                setValue(`cart_items.${index}.buyer_line_total`, 0);
                setValue(`cart_items.${index}.seller_extended`, 0);
            }
        }
    }
    const calculateTotalPrice = () => {
        if (!(isSavedBom && createPoDataFromSavedBom && !watch('is_draft_po'))) {
            const materialTotal = +(watch('buyer_po_price') ?? 0);
            let totalPurchaseOrderPrice: number = 0;
            if (materialTotal) {
                totalPurchaseOrderPrice += materialTotal + (+(watch('sales_tax') ? watch('sales_tax') : 0)) + (+(watch('deposit_amount') ?? 0)) + (+(watch('processing_fees') || 0))
            }
            setValue('totalPurchase', parseFloat(totalPurchaseOrderPrice));
            if(((isCreatePoDirty || location.state?.from === 'bomPdfExtractor') && poHeaderFormWatch?.()) ){
                updateLocalStoreQuote(createPoResultCopyList);
            }
            
        }
    }

    const paymentMethodChangeHandler = async () => {
        if (getValues('buyer_po_price')) {
            if (watch('payment_method') === 'ach_credit' || watch('payment_method') === 'card') {
                // const depositValue = await calculateDepositAmount();
                // setValue('deposit_amount', depositValue);
                await handleSalesTax();
            }
            else
                setValue('deposit_amount', 0);
        }
    }

    const calculateDepositAmount = async () => {
        try {
            const price = parseFloat(getValues("buyer_po_price") ?? "0").toFixed(2)
            const payload = {
                data: {
                    payment_method: getValues("payment_method"),
                    price: Number(price),
                },
            };
            const depositResponse = await calculateDepositTax.mutateAsync(payload);
            return depositResponse.data.data.deposit_amount;
        } catch (error) {
            console.error(error)
        }
    };

    const calculateTotalPricing = async () => {
        await handleSalesTax();
        // let depositValue = 0;
        // if (getValues("payment_method") === "ach_credit") {
        //     depositValue = await calculateDepositAmount();
        // }
        // setValue("deposit_amount", depositValue);
    }

    const handleSalesTax = async () => {
        try {
            // setHandleSubmitValidation(false);
            const matTotal = parseFloat(getValues('buyer_po_price'));
            if (!matTotal) {
                // setApiCallInProgress(false);
                return;
            }
            const taxCounter = salesTaxCounter + 1;
            setSalesTaxCounter(taxCounter)
            let payloadData: any = { ...getValues(), ...poHeaderFormWatch?.() };
            payloadData.cart_items = Object.values(createPoResultCopy)?.map((item: any) => {
                if (!item.descriptionObj || (item.descriptionObj && Object.keys(item.descriptionObj).length === 0) || (item && 'line_status' in item && item.line_status === "SKIPPED") || (item && !item.qty)) return null;
                const itemCopy = { ...item }
                itemCopy.descriptionObj = undefined;
                itemCopy.price = item.buyer_price_per_unit;
                itemCopy.extended = item.buyer_line_total;
                return itemCopy
            }).filter(Boolean)
            payloadData.price = payloadData.buyer_po_price;
            payloadData.recevingHours = undefined;
            payloadData.deposit_amount = undefined;
            payloadData.totalPurchase = undefined;
            payloadData.counter = salesTaxCounter + 1
            const payload = {
                data: payloadData,
            };
            const salesResponse = await calculateOrderCharges.mutateAsync(payload);
            const salesTaxCounterResponse = salesResponse.data.data.counter;
            if (salesTaxCounterResponse === taxCounter && watch('buyer_po_price')) {
                console.log("salesResponse @>>>>>>>", salesResponse)
                setValue("sales_tax", parseFloat(salesResponse.data.data.sales_tax));
                setValue("deposit_amount", parseFloat(salesResponse.data.data.deposit));
                setValue("processing_fees", parseFloat(salesResponse.data.data.processing_fees));
            }
        } catch (error) {

        }
        // setApiCallInProgress(false);
    };


    const updateAllValues = (priceMapping: any = {}, createPoList: any) => {
        Object.entries(createPoList).forEach(([index, item]: [string, any]) => {
            if (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) {
                const _selected = item.descriptionObj;
                const productId = item.descriptionObj.Product_ID;
                const priceUnit = item.price_unit;
                const prices = priceMapping[productId];
                if (!priceUnit || !item?.price_um) {
                    const priceUnitMData = _selected?.PUM_Dropdown_Options?.split(",").filter((item: any) => !(item.trim().toLowerCase() === priceUnits.net_ton || item.trim().toLowerCase() === 'net ton'));
                    item.price_um = priceUnitMData;

                    if (!initialData?.cart_items?.[index]?.price_unit) {
                        let priceUnit = priceUnitMData[0];
                        if (initialData?.cart_items?.[index]?.bom_line_id && initialData?.cart_items?.[index]?.qty_unit) priceUnit = initialData?.cart_items?.[index]?.qty_unit;
                        if (item?.bom_line_id && item?.qty_unit) priceUnit = item?.qty_unit;
                        item.price_unit = priceUnit;
                    } else {
                        item.price_unit = initialData?.cart_items?.[index]?.price_unit;
                    }
                    if (_selected && item.qty > 0) {
                        const unit = item.price_unit || _selected.PUM_Dropdown_Options.split(",")[0];
                        const umVal = getValUsingUnitKey(_selected, unit, newPricingPrefix);
                        item.buyer_price_per_unit = umVal;
                        item.seller_price = umVal;
                    }
                    else {
                        item.buyer_price_per_unit = 0;
                        item.seller_price = 0;
                    }
                }
                const priceUnitMData = item.price_um || [];
                priceUnitMData.forEach((unit: any) => {
                    if (unit.toLowerCase() !== "net ton" || unit.toLowerCase() !== priceUnits.net_ton) {
                        item.descriptionObj[newPricingPrefix + unit] = prices?.[unit] || prices?.[unit.toLowerCase()];
                    }
                })
                if (_selected && Object.keys(_selected).length > 0) {
                    const updateQtyUnit = item.qty_unit?.toLowerCase();
                    const updatePriceUnit = item.price_unit?.toLowerCase();
                    item.qty_unit = updateQtyUnit;
                    item.price_unit = updatePriceUnit;

                    if (item.line_status === "SKIPPED") {
                        return;
                    }
                    // updateLineItem(index);
                    const qtyVal = item.qty;
                    const qtyUnit = item.qty_unit;
                    const priceUnit = item.price_unit;
                    if (!item.qty_um) {
                        item.qty_um = _selected.QUM_Dropdown_Options.split(",");
                    }
                    if(!qtyUnit){
                        item.qty_unit = initialData?.cart_items?.[index]?.qty_unit || _selected.QUM_Dropdown_Options.split(",")[0].toLowerCase();
                    }
                    const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
                    const updatedUnit = unit.toLowerCase() === priceUnits.ea ? priceUnits.pc : unit;
                    const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);

                    if (_selected && item.qty > 0) {
                        const unit = item.price_unit || _selected.PUM_Dropdown_Options.split(",")[0];
                        const umVal = getValUsingUnitKey(_selected, unit, newPricingPrefix);
                        item.buyer_price_per_unit = umVal;
                        item.seller_price = umVal;
                    }
                    else {
                        item.buyer_price_per_unit = 0;
                        item.seller_price = 0;
                    }
                    if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                        const buyerPricePerUnit = parseFloat(getValUsingUnitKey(_selected, unit, newPricingPrefix));
                        item.buyer_calculation_price = buyerPricePerUnit;
                        item.seller_calculation_price = buyerPricePerUnit;
                        const totalVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                        const extendedValue = buyerPricePerUnit * qtyVal;
                        const totalSellerVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                        item.buyer_line_total = +totalVal;
                        item.seller_extended = +totalSellerVal;
                        item.extendedValue = +extendedValue;
                        setFormErrors((prevErrors: any) => {
                            const newErrors = { ...prevErrors };
                            delete newErrors[index];
                            return newErrors;
                        });
                        // clearErrors(`cart_items.${index}.qty`);
                        // trigger(`cart_items.${index}.qty`);
                    } else {
                        if (_selected) setFormErrors((prevErrors: any) => ({
                            ...prevErrors,
                            [index]: {
                                qty: true,
                                product: false,
                                qtyEmpty: false
                            }
                        }));
                        if (qtyVal === null) item.qty = '';
                        item.buyer_line_total = 0;
                        item.seller_extended = 0;
                        item.price_unit = '';
                        item.buyer_price_per_unit = 0;
                        item.seller_price = 0;
                        if (qtyVal < 1) {
                            setFormErrors((prevErrors: any) => ({
                                ...prevErrors,
                                [index]: {
                                    qty: false,
                                    product: false,
                                    qtyEmpty: true
                                }
                            }));
                            // clearErrors(`cart_items.${index}.qty`);
                        }
                    }
                    // if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                    //     if (!priceUnit) resetPricePerUnitFields(index, _selected)
                    // }
                    // const visibleIds = watch('cart_items').map((item: any) => item.a);
                    // const numericIndex = Number(index);
                    // const indexToSet = numericIndex - (pageIndex * itemsToAddOnScroll);
                    // if (visibleIds.includes(numericIndex)) {
                    //     setValue(`cart_items.${indexToSet}`, item)
                    // }
                    // setValue(`cart_items.${index}`, createPoResultCopy[index])
                }
            } else {
                setFormErrors((prevErrors: any) => ({
                    ...prevErrors,
                    [index]: {
                        qty: false,
                        product: true,
                        qtyEmpty: false
                    }
                }));
            }
        });
        calculateMaterialTotalPrice();
    }


    const willGearUpdate = (weight: number) => {
        const referenceData = useGlobalStore.getState().referenceData;
        const weightRanges = referenceData?.ref_weight_price_brackets;
        const currentGearMinWeight = weightGear === 0 ? 0 : weightRanges[weightGear].min_weight ;
        const currentGearMaxWeight = weightGear === weightRanges.length -1 ? Number.MAX_SAFE_INTEGER : weightRanges[weightGear].max_weight;
        if ( weight >= currentGearMinWeight && weight <= currentGearMaxWeight) {
            return false;
        }
        return true;
    }
    
    const resetQtyAndPricePerUnitFields = (index: number, product: any) => {
        const qtyUnitMData = product.QUM_Dropdown_Options.split(",")
        setValue(`cart_items.${index}.qty_um`,qtyUnitMData);
        if (!initialData?.cart_items?.[index]?.qty_unit) {
            setValue(`cart_items.${index}.qty_unit`, qtyUnitMData[0])
        }
        resetPricePerUnitFields(index, product);
    }

    const formatCartItems = (products, draftId = null) => {
        return products
        .filter((item: any) => draftId ? (item?.draft_line_id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0)) : ((item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED"))
        .map((item, index) => {
            if (draftId ? (item?.draft_line_id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0)) : ((item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED")) {
                const buyerPricingLbKey = `${newPricingPrefix}${priceUnits.lb}`;
                let formattedQtyUnit = '';
                let formattedPriceUnit = '';
                if(draftId && item?.draft_line_id && !item?.qty_unit){
                    formattedQtyUnit = initialData?.cart_items[index]?.qty_unit
                }else{
                    formattedQtyUnit = item?.qty_unit?.toUpperCase();
                }

                if(draftId && item?.draft_line_id && !item?.price_unit){
                    formattedPriceUnit = initialData?.cart_items[index]?.price_unit
                }else{
                   formattedPriceUnit = item?.price_unit?.toUpperCase();
                }
                const cartItem = {
                    "line_id": index + 1,
                    "description": item?.descriptionObj?.UI_Description ?? '',
                    "qty": (draftId && item?.draft_line_id && !item?.qty) ? initialData?.cart_items[index]?.qty : item?.qty || '',
                    "qty_unit": formattedQtyUnit,
                    "product_tag": item?.product_tag ?? '',
                    "product_tag_mapping_id": item?.descriptionObj?.product_tag ?? '',
                    "price": (draftId && item?.draft_line_id && !item?.buyer_price_per_unit) ? initialData?.cart_items[index]?.price_per_unit : item?.buyer_price_per_unit ?? '',
                    "price_unit": formattedPriceUnit,
                    "extended": item?.buyer_line_total ?? '',
                    "product_id": (draftId && item?.draft_line_id && !item?.descriptionObj?.Product_ID) ? initialData?.cart_items[index]?.product_id : item?.descriptionObj?.Product_ID ?? '',
                    "reference_product_id": item?.descriptionObj?.id ?? '',
                    "shape": item?.descriptionObj?.Key2 ?? '',
                    "seller_price": item?.seller_price ?? '',
                    "seller_extended": item?.seller_extended?.toFixed(2) ?? '',    
                    "buyer_pricing_lb": item?.descriptionObj?.[buyerPricingLbKey] ?? '',
                    "domestic_material_only": item?.domesticMaterialOnly ?? false,
                    "buyer_calculation_price": item?.buyer_calculation_price ?? '',
                    "seller_calculation_price": item?.seller_calculation_price ?? '',
                    "line_status": item?.line_status ?? '',
                    "id": draftId ? item?.draft_line_id : undefined,
                    "is_line_removed": draftId ? (!item?.descriptionObj?.Product_ID ? true : false) : undefined
                };
                return cartItem
            }
            return null
        });
    }

    const getCartItems = () =>{
        const formattedItems = createPoResultCopyList.filter(item => item?.descriptionObj?.UI_Description || item?.line_status).map((item, index) => ({
            description: descriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            otherDescription: getOtherDescriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            product_tag: item?.product_tag ?? '',
            domesticMaterialOnly: item?.domesticMaterialOnly ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item?.qty ?? ''),
            qty_unit: item?.qty_unit?.toLowerCase() ?? '',
            price_unit: item?.price_unit?.toLowerCase() ?? '',
            extended: formatToTwoDecimalPlaces(item?.buyer_line_total ?? ''),
            price: item?.price_unit?.toLowerCase() === priceUnits.lb ? format4DigitAmount(item?.buyer_price_per_unit ?? '') : formatToTwoDecimalPlaces(item?.buyer_price_per_unit ?? ''),
            line_weight: location.pathname === routes.savedBom ? formatToTwoDecimalPlaces(item?.line_weight ?? '') : calculateLineWeight(item),
            line_weight_unit: "Lb", 
            line_no: index,
            po_line: index.toString(),
            descriptionObj:item?.descriptionObj ?? {},
            extendedValue:item?.extendedValue ?? '',
            line_status: item?.line_status ?? ''
        }));
        return formattedItems
    }
    const calculateLineWeight = (data) => {
        let lineWeight = 0;
        const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
        if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0){
            const qtyUnit = data.qty_unit ? data.qty_unit : data.descriptionObj.QUM_Dropdown_Options.split(",")[0];
            const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`]?.replace(/[\$,]/g, '')) || 0
            const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`]?.replace(/[\$,]/g, '')) || 0;
            const lbsPerFt = orderIncrementLb / orderIncrementFt
            const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
            const updatedQtyUnit = qtyUnit.toLowerCase() === priceUnits.ea ? priceUnits.pc : qtyUnit.toLowerCase();
            const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${updatedQtyUnit}`]?.replace(/[\$,]/g, '')) || 0;
            lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
        }
        return formatToTwoDecimalPlaces(lineWeight);
    } 

    const handleUpdateSelectedData = async(selectedQuoteFetchLatestData: any) => {
        clearLocal(localStorageKeys.poQuoting);
        setUpdatedDraftId(null);
        await handleLoadQuoteData(selectedQuoteFetchLatestData);
        setIsConvertingToPo(null);

    }


    const handleLoadQuoteData = async (item: any) => {
        setShowLoader(true);
        setIsCreatePoDirty(false);
        const draftLines = await getDraftLines(item.id);
        updateQuoteData(item, draftLines?.data);
        // setShowLoader(false);
    }

    const updateQuoteData = (quoteData: any, quoteLinesData: any) => {
        let createPoData: any = {
            ...quoteData,
            id: quoteData.id,
            isEdit: !quoteData.pricing_expired,
            buyer_internal_po: quoteData.buyer_internal_po,
            delivery_date: quoteData.delivery_date,
            shipping_details: quoteData.shipping_details,
            order_type: quoteData.order_type,
            buyer_po_price: quoteData.buyer_po_price,
            created_date: quoteData.created_date,
            deposit_amount: quoteData.deposit_amount,
            freight_amount: quoteData.freight_amount,
            last_price_updated_date: quoteData.last_price_updated_date,
            payment_method: quoteData.payment_method,
            pricing_expired: quoteData.pricing_expired,
            sales_tax: quoteData.sales_tax,
            source: quoteData.source,
            subscription: quoteData.subscription,
            total_weight: quoteData.total_weight
        }
        // const headerInfo = {
        // }
        // createPoData.headerInfo = headerInfo;
        createPoData.cart_items = quoteLinesData?.map((line: any) => {
            return {
                ...line,
                descriptionObj: productMapping[line.product_id]
            }
        });
        setSelectedQuote(createPoData);
        setCreatePoData(createPoData);
    }

    return {
        updateLineProduct,
        calculateMaterialTotalPrice,
        pricePerUnitChangeHandler,
        removeLineItem,
        handleCreatePOSearch,
        saveUserLineActivity,
        handlePriceIntegration,
        updateLineItem,
        resetPricePerUnitFields,
        handleSubmitValidation,
        resetQtyAndPricePerUnitFields,
        willGearUpdate,
        formatCartItems,
        getCartItems,
        handleUpdateSelectedData,
        handleLoadQuoteData
    }
}
export default useCreatePoPriceCalculation;
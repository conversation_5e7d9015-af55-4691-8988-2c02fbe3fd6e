
import {  useEffect, useState, useRef } from "react";
import styles from "../CreatePo.module.scss";
import { Controller } from "react-hook-form";
import { Autocomplete, ClickAwayListener, Fade, Tooltip } from "@mui/material";
import { CustomMenu } from "../../CustomMenu";
import clsx from "clsx";
import { ReactComponent as DomesticCheckTextIcon } from '../../../../assets/New-images/Domestic-Check-Text.svg';
import { ReactComponent as DownArrowIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import { v4 as uuidv4 } from 'uuid';
import { formatDollarPerUnit, searchProducts, getValidSearchData, useGlobalStore, MinSearchDataLen, formatToTwoDecimalPlaces, priceUnits, get2DigitFormater, get4DigitFormater, getValUsingUnitKey, orderIncrementPrefix, useCreatePoStore, useOrderManagementStore, dateTimeFormat, useGetDeliveryDate, useBuyerSettingStore, userRole } from '@bryzos/giss-ui-library';
import CustomTextField from "src/renderer2/component/CustomTextField";
import InputWrapper from "src/renderer2/component/InputWrapper";
import PriceHistoryDropdown from "src/renderer2/component/PriceHistoryDropdown";
import Calendar from "src/renderer2/component/Calendar/Calendar";
import { calculateBuyerTotalOrderWeightForGear, calculateTotalWeightForProduct } from "src/renderer2/utility/pdfUtils";
import { LargeProductsNameList, routes } from "src/renderer2/common";
import { useDebouncedValue } from "@mantine/hooks";
import { useLocation } from "react-router-dom";
import dayjs from 'dayjs';
import { removeDecimalZero } from "src/renderer2/helper";

const SAME_TAG_ERROR_MESSAGE =
  "Same part? <br />You have applied this part number to another product already.";

const CreatePoTile = ({
  index,
  actualIndex,
  register,
  fields,
  products,
  control,
  setValue,
  watch,
  errors,
  getValues,
  userPartData,
  sessionId,
  selectedProduct,
  searchStringData,
  setSearchString,
  setLineSessionId,
  lineSessionId,
  handleCreatePOSearch,
  updateLineProduct,
  removeLineItem,
  pricePerUnitChangeHandler,
  saveUserLineActivity,
  orderInfoIsFilled,
  setDisableBidBuyNow,
  openAddLineTab,
  setOpenDeliveryToDialog,
  hidePoLineScroll,
  setHidePoLineScroll,
  scrollToTop,
  calculateMaterialTotalPrice,
  setIsCreatePoDirty,
  isHeaderDetailsConfirmed,
  cameFromSavedBom,
  handlePriceIntegration,
  clearErrors,
  updateLineItem,
  createPoResultCopy,
  setCreatePoResultCopy,
  setFormErrors,
  handleFilterFieldsData,
  willGearUpdate,
  handleLoadNextItems,
  pageIndex,
  lineItemsToLoad,
  debouncedSearchString,
  setSelectedProduct,
  lineSessionIdChanges,
  setLineSessionIdChanges,
  formErrors,
  updateLocalStoreQuote,
  handleStoreUndoStack,
  setUndoStackObject,
  undoStackObject,
  calculateExtendedPrice,
  handleCtrlClick,
  selectedCancelLines,
  isStateZipValChange,
  setIsOrderLineChanges
}) => {
  const location = useLocation();
  const { userData , referenceData} = useGlobalStore();
  const isCreatePoDirty = useCreatePoStore(state => state.isCreatePoDirty);
  const isEditingPo = useOrderManagementStore(state => state.isEditingPo);
  const deliveryAllowedDates = useBuyerSettingStore(state => state.deliveryAllowedDates);
  const disableDeliveryDate = useBuyerSettingStore(state => state.disableDeliveryDate);
  const [descriptionInput, setDescriptionInput] = useState("");
  const [sameTagErrorMsg, setSameTagErrorMsg] = useState(null);
  const [openDescription, setOpenDescription] = useState(false);
  const [isQtyInEditMode, setIsQtyInEditMode] = useState(false);
  const [showDomesticMaterialOnly, seShowDomesticMaterialOnly] = useState(true);
  const [enableRejectSearchAnalytic, setEnableRejectSearchAnalytic]= useState(false);
  const [focusPlaceHolder, setFocusPlaceHolder]= useState(false);
  const [hasSearchResults, setHasSearchResults] = useState(false);
  const [isDescriptionModeEnabled, setIsDescriptionModeEnabled] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isDomesticFocused, setIsDomesticFocused] = useState(false);
  const lineNumberRef = useRef(null);
  const [checkAfterFocus, setCheckAfterFocus] = useState(false);
  const [isTabIndexProcessing, setIsTabIndexProcessing] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [debouncedQty] = useDebouncedValue(createPoResultCopy[actualIndex]?.qty, 400);
  const [debouncedExtended] = useDebouncedValue(createPoResultCopy[actualIndex]?.buyer_line_total, 400);
  const [isDataChanged, setIsDataChanged] = useState(false);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs | null>(null);

  const isSeller = userData?.data?.type === userRole.sellerUser;

  const getDeliveryDate = useGetDeliveryDate();

  // Initialize selected date from form value
  useEffect(() => {
    const formDeliveryDate = watch(`cart_items.${index}.delivery_date`);
    if (formDeliveryDate) {
      setSelectedDate(dayjs(formDeliveryDate, dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit));
    }
  }, [watch(`cart_items.${index}.delivery_date`)]);

  const isOrderLineDisabled = !(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) && !orderInfoIsFilled;

  // Calendar handlers
  const handleOpenCalendar = () => {
    setUndoStackObject({name: "delivery_date", value: watch(`cart_items.${index}.delivery_date`), id: `cart_items.${index}.delivery_date`, from: "line"});
    if (watch('isEdit')) {
      setIsCalendarOpen(true);
    }
  };

  const handleDateSelect = (date: Date) => {
    const formattedDate = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
    setSelectedDate(dayjs(date));
    setValue(`cart_items.${index}.delivery_date`, formattedDate);
    setIsCreatePoDirty(true);
    setIsCalendarOpen(false);
    handleStoreUndoStack({...undoStackObject, currentValue: formattedDate, actualIndex: actualIndex, index: index, uniqueId: actualIndex});
    setUndoStackObject({});
  };

  useEffect(() => {
    // setValue(`cart_items.${index}.line_session_id`, uuidv4());
    // if (index > 0) {
    //   saveUserActivity();
    // }
    qtyEditModeCloseHandler();
  }, []);

  useEffect(() => {
    const description = getValues(`cart_items.${index}.descriptionObj`);


    if (referenceData && !watch(`cart_items.${index}.domesticMaterialOnly`)) {
      setValue(
        `cart_items.${index}.domesticMaterialOnly`,
        false
      );
    }
    if (description && Object.keys(description).length > 0) {
      if (!description.domestic_material_only) {
        seShowDomesticMaterialOnly(false);
        setValue(`cart_items.${index}.domesticMaterialOnly`, null);
      } else {
        seShowDomesticMaterialOnly(true);
      }
    } else {
      seShowDomesticMaterialOnly(true);
    }
  }, [referenceData, watch(`cart_items.${index}.descriptionObj`)]);

  useEffect(() => {
    const product_tag = getValues(`cart_items.${index}.product_tag`);
    const description = getValues(`cart_items.${index}.descriptionObj`);

    if (description && Object.keys(description).length > 0) {
      setValue(`cart_items.${index}.description`, description.UI_Description);
      setValue(`cart_items.${index}.shape`, description.Key2);
      setValue(`cart_items.${index}.product_id`, description.Product_ID);
      setValue(`cart_items.${index}.reference_product_id`, description.id);
      if (!description.domestic_material_only) {
        seShowDomesticMaterialOnly(false);
        setValue(`cart_items.${index}.domesticMaterialOnly`, null);
      } else {
        seShowDomesticMaterialOnly(true);
      }
    } else {
      seShowDomesticMaterialOnly(true);
    }

    if (description && Object.keys(description).length > 0) {
      if (userPartData && Object.keys(userPartData)?.length) {
        let isMappingExist = false;
        const i = Object.keys(userPartData).findIndex((key)=>{
          if(userPartData[key] === product_tag){
            isMappingExist = true;
            if(+key === description.Product_ID){
               return true;
            }
          }
        })
        setSameTagErrorMsg(i > -1 ? null : SAME_TAG_ERROR_MESSAGE);
        if (!isMappingExist) {
          setSameTagErrorMsg(null);
        }
      }
    } else {
      setSameTagErrorMsg(null);
      setValue(`cart_items.${index}.product_tag`, "");
    }
  }, [
    watch(`cart_items.${index}.product_tag`),
    watch(`cart_items.${index}.descriptionObj`),
  ]);

  useEffect(() => {
    if (!getValues(`cart_items.${index}.descriptionObj`) || formErrors[actualIndex]?.qty) {
      if(checkAfterFocus){
        setIsQtyInEditMode(true);
      }
    }
  }, [
    getValues(`cart_items.${index}.descriptionObj`),
    formErrors[actualIndex]?.qty,
  ]);

//   useEffect(() => {
//     if(debouncedSearchString && lineSessionId[actualIndex] === watch(`cart_items.${index}.line_session_id`)){
//         handleCreatePOSearch(searchStringData, null, lineSessionId[actualIndex])
//     }
// }, [debouncedSearchString])

useEffect(() => {
  if(selectedProduct && selectedProduct?.UI_Description?.toLowerCase() === descriptionInput?.toLowerCase() && lineSessionId[actualIndex] === watch(`cart_items.${index}.line_session_id`)){
    setEnableRejectSearchAnalytic(false)
    handleCreatePOSearch(searchStringData, 'Accept', lineSessionId[actualIndex])
    setSelectedProduct(null)
  }
},[selectedProduct])

useEffect(() => {
  if((!selectedProduct && descriptionInput.length >= MinSearchDataLen) || (descriptionInput.length >= MinSearchDataLen)){
    setSearchString(descriptionInput);
    let lineSessionIdChanges = lineSessionId[actualIndex];
    if(!lineSessionId[actualIndex]){
      lineSessionIdChanges = uuidv4();
      setLineSessionId(prev => {
        const newPrev = [...prev];
        newPrev[actualIndex] = lineSessionIdChanges;
        return newPrev;
      });
      setValue(`cart_items.${index}.line_session_id`, lineSessionIdChanges);
    }
    setLineSessionIdChanges(lineSessionIdChanges);
  }
  if(descriptionInput.length === 1 ){
    setEnableRejectSearchAnalytic(true)
  }
  if(descriptionInput.length === 0 && searchStringData.length !== 0 && searchStringData.length >= MinSearchDataLen && enableRejectSearchAnalytic){
    handleCreatePOSearch(searchStringData, 'Reject', lineSessionId[actualIndex])
    setEnableRejectSearchAnalytic(false)
  }
},[descriptionInput])

  function display(data) {
    const lines = data.UI_Description.split("\n");
    const firstLine = lines[0];
    const restLines = lines.slice(1);

    return (
      <>
        <p className={clsx('liFisrtLine', styles.liFisrtLine)}>{firstLine}</p>
        {restLines.map((line, index) => (
          <p key={index}>{line}</p>
        ))}
      </>
    );
  }

  const qtyEditModeCloseHandler = () => {
    if (
      getValues(`cart_items.${index}.descriptionObj`) &&
      getValues(`cart_items.${index}.qty`) &&
      !formErrors[actualIndex]?.qty
    ) {
      setIsQtyInEditMode(false);
    } else {
      if(checkAfterFocus){
        setIsQtyInEditMode(true);
      }
    }
  };

  const descriptionEditModeCloseHandler = () => {
   setIsDescriptionModeEnabled(false)
  };

  const [qtyDebounceTimer, setQtyDebounceTimer] = useState<ReturnType<typeof setTimeout> | null>(null);
  const [qtyUnitDebounceTimer, setQtyUnitDebounceTimer] = useState<ReturnType<typeof setTimeout> | null>(null);

  const quantityChangeHandler = (event:any) => {
    const qty = quantitySizeValidator(event, index);
    if (qtyDebounceTimer) clearTimeout(qtyDebounceTimer);
    setQtyDebounceTimer(
      setTimeout(() => {
        createPoResultCopy[actualIndex].qty = qty;
        calculateExtendedPrice(actualIndex);
      }, 400)
  );
    // const totalWeight = calculateTotalWeightForProduct(watch(`cart_items`));
    // setValue('totalWeight', totalWeight);
    // handlePriceIntegration(actualIndex);
    // updateValue(index);
  }

  const dollerPerUmFormatter = (umVal) => {
    const price_unit = getValues(`cart_items.${index}.price_unit`);
    return formatDollarPerUnit(price_unit,umVal,index);
  }

  const saveUserActivity = (isRemovingLine = false) => {
    if(watch(`cart_items.${index}.descriptionObj`)){
      saveUserLineActivity(sessionId, isRemovingLine, index);
    }
  }

  const getPlaceholder = (index) => {
    const cartItems = watch('cart_items');
    const firstEmptyIndex = cartItems ? cartItems.findIndex(item => !item?.descriptionObj || !item?.descriptionObj?.UI_Description) : -1;
    if (focusPlaceHolder) {
      return "EX:  BEAM 4\" x 13 . . .\nOR ENTER A TAGGED PART #";
    } else if (index === firstEmptyIndex && !orderInfoIsFilled) {
      return "ALL HEADER INFORMATION MUST BE ENTERED BEFORE ENTERING LINE ITEMS OR UPLOADING A BOM";
    } else {
      return "";
    }
  };

  const skipLineHandler = async (lineStatus: string) => {
    // setValue(`cart_items.${index}.line_status`, 'SKIPPED');
    // setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(watch('cart_items')));
    // updateValue(index);
    
    setIsCreatePoDirty(true)
    createPoResultCopy[actualIndex].line_status = lineStatus;
    const totalWeight = calculateTotalWeightForProduct(Object.values(createPoResultCopy));
    setValue('total_weight', totalWeight);
    const isGearUpdated = willGearUpdate(totalWeight);
    await handlePriceIntegration();
    handleFilterFieldsData();
    calculateMaterialTotalPrice();
    // Clear all errors for this specific cart item index
    if (formErrors[actualIndex]) {
      // clearErrors(`cart_items.${index}`);
      setFormErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors[actualIndex];
        return newErrors;
      });
    }
  }

  const addLineHandler = async () => {
    // setValue(`cart_items.${index}.line_status`, 'APPROVED');
    setIsCreatePoDirty(true)
    createPoResultCopy[actualIndex].line_status = 'APPROVED';
    const totalWeight = calculateTotalWeightForProduct(Object.values(createPoResultCopy));
    setValue('total_weight', totalWeight);
    const isGearUpdated = willGearUpdate(totalWeight);
    await handlePriceIntegration();
    handleFilterFieldsData();
    calculateMaterialTotalPrice();
    // setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(watch('cart_items')));
    // updateValue(index);
    // calculateMaterialTotalPrice();
    // updateValue(index, actualIndex);
  }

  const deleteLineHandler = () => {
    removeLineItem(index, actualIndex);
  }

  
  const quantitySizeValidator = (e: any, index: any) => {
    if (isNaN(e.target.value)) {
        return;
    }
    if (e.target.value) {
        const arr = e.target.value.split(".");
  
        if (arr.length > 1) {
            // e.target.value = arr[0].slice(0, 8) + "." + arr[1].slice(0, 2);
            e.target.value = arr[0].slice(0, 8) + "." + arr[1];
            setValue(`cart_items.${index}.qty`, e.target.value);
        } else {
            e.target.value = arr[0].slice(0, 8);
            setValue(`cart_items.${index}.qty`, e.target.value);
        }
    }
    register(`cart_items.${index}.qty`).onChange(e);
    return e.target.value;
  };

  useEffect(() => {
    if (isCreatePoDirty && watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) {
      const createPoResultCopySpread = {...createPoResultCopy};
      createPoResultCopySpread[actualIndex] = {
        ...(createPoResultCopySpread[actualIndex] || {}),
        ...watch(`cart_items.${index}`),
      }
      setCreatePoResultCopy(createPoResultCopySpread);
      updateLocalStoreQuote(Object.values(createPoResultCopySpread));
    }
  }, [watch(`cart_items.${index}.line_session_id`), watch(`cart_items.${index}.domesticMaterialOnly`), watch(`cart_items.${index}.descriptionObj`), watch(`cart_items.${index}.product_tag`), watch(`cart_items.${index}.qty_unit`), watch(`cart_items.${index}.price_unit`), watch(`cart_items.${index}.buyer_price_per_unit`), watch(`cart_items.${index}.buyer_line_total`), watch(`cart_items.${index}.delivery_date`)])

  useEffect(()=>{
    if(watch('isEdit')){
    calculateMaterialTotalPrice();
    }
  },[debouncedExtended]);

  const handleLineClick = (e: React.MouseEvent) => {
    if ((e?.shiftKey && watch(`cart_items.${index}.id`) && location.pathname === routes.orderManagementPage) && !(isEditingPo && !watch('seller_name'))) {
      handleCtrlClick(watch(`cart_items.${index}`));
      return;
    }
  }

  return (
    <tr className={clsx(styles.marginBottom,isSeller &&  styles.sellerUserLine, (selectedCancelLines?.includes(watch(`cart_items.${index}.id`))) && styles.selectedLine)} id={`createPoTile-${actualIndex}`} onClick={handleLineClick}>
      <td>
        <div className={styles.prodId}>
          {/* <span className={styles.numberContainer}>{index + 1}</span> */}
          <div className={styles.domesticMaterialCheckbox} >
            <label className={styles.lineNumberContainer} data-hover-video-id={ orderInfoIsFilled ? "domestic-only-po" : ""}>
              <input
                type="checkbox"
                {...register(`cart_items.${index}.domesticMaterialOnly`)}
                checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                disabled={watch(`cart_items.${index}.line_status`) === 'SKIPPED'}
                onChange={(e) => {
                  if (!watch('isEdit') || !showDomesticMaterialOnly || !(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) || watch(`cart_items.${index}.line_status`) === 'SKIPPED' || isStateZipValChange) {
                    return; // Prevent changes when visually disabled
                  }
                  register(`cart_items.${index}.domesticMaterialOnly`).onChange(e);
                  setIsCreatePoDirty(true);
                  saveUserActivity();
                  if(setIsOrderLineChanges && watch('seller_name')) {
                    setIsOrderLineChanges(true);
                  }
                }}
                className={styles.hiddenCheckbox}
                // We keep it enabled for keyboard navigation but mark it as aria-disabled
                aria-disabled={(!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED' || isStateZipValChange)}
              />
              <span
                className={clsx(
                  styles.customNumberToggle,
                  watch(`cart_items.${index}.domesticMaterialOnly`) ? styles.active : "",
                  (!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED' || isStateZipValChange) ? styles.disabled : "",
                  watch(`cart_items.${index}.line_status`) === 'SKIPPED' ? styles.domesticSkipDisabled : ""
                )}
                onBlur={() => {
                  setIsDomesticFocused(false);
                  if(handleStoreUndoStack){
                    handleStoreUndoStack({...undoStackObject, currentValue: watch(`cart_items.${index}.domesticMaterialOnly`), from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex});
                  }
                  setUndoStackObject({});
                }}
                onFocus={() => {
                  console.log("focus new data");
                  setIsHovering(false); // Turn off hover style when focused
                  setIsDomesticFocused(true);
                  setUndoStackObject({name: `domesticMaterialOnly`, value: watch(`cart_items.${index}.domesticMaterialOnly`), id: `cart_items.${index}.domesticMaterialOnly`, from: "line"});
                }}
                onKeyDown={(e) => {
                  // Always become active when receiving keyboard focus, even when visually disabled
                  setIsDomesticFocused(true);
                  
                  // Allow toggling with Enter or Space only when not visually disabled
                  const isVisuallyDisabled = !watch('isEdit') || !showDomesticMaterialOnly || 
                    !(watch(`cart_items.${index}.descriptionObj`) && 
                      Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) || watch(`cart_items.${index}.line_status`) === 'SKIPPED';
                      
                  if ((e.key === 'Enter' || e.key === ' ') && !isVisuallyDisabled) {
                    e.preventDefault();
                    const newValue = !(watch(`cart_items.${index}.domesticMaterialOnly`) ?? false);
                    setValue(`cart_items.${index}.domesticMaterialOnly`, newValue);
                    setIsCreatePoDirty(true);
                    saveUserActivity();
                  }
                  
                  // Handle tab navigation
                   if (e.key === 'Tab' && !e.shiftKey) {
                    // Forward tab to next element
                    setIsDomesticFocused(false);
                  }else if(e.key === 'Tab' && e.shiftKey){
                    if(index === 0){
                      e.preventDefault();
                    const uploadBillOfMaterialInput = document.getElementById('upload-bill-of-material');
                    if (uploadBillOfMaterialInput instanceof HTMLElement) {
                      uploadBillOfMaterialInput.focus();
                    }
                    }else{
                      const qtyInput = document.querySelector(`input[id="cart_items.${actualIndex - 1}.qty"]`);
                      if (qtyInput instanceof HTMLElement && !qtyInput.disabled) {
                        e.preventDefault();
                        qtyInput.focus();
                      }
                    }
                  }
                }}
                role="checkbox"
                id={"domesticMaterialCheckbox-"+actualIndex}
                aria-checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                aria-disabled={(!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED' || isStateZipValChange)}
                ref={lineNumberRef}
                tabIndex={(!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED' || isStateZipValChange) ? -1 : 0}
              >
                {actualIndex + 1}
              </span>
              <span className={clsx(styles.usaOnlyText, watch(`cart_items.${index}.domesticMaterialOnly`) ? "" : styles.visibilityHidden)}>USA<br />ONLY</span>
            </label>
          </div>
          {(isHovering && showDomesticMaterialOnly && !watch(`cart_items.${index}.domesticMaterialOnly`)) && <DomesticCheckTextIcon className={styles.domesticCheckText} />}
        </div>
      </td>
      {watch(`cart_items.${index}.line_status`) === 'SKIPPED' ? (
        <td colSpan={5} className={styles.skippedLineTd}>
          <div className={styles.skippedLineContent}>
            <span>Placeholder (manually skipped)</span>
            <div className={styles.skippedLineActionBtns}>
              <button className={styles.btn} onClick={addLineHandler}>
                <span>
                  Undo skip line
                </span>
              </button>
              <button className={styles.btn} onClick={() => skipLineHandler('DELETED')}>
                <span>
                  Delete line
                </span>
              </button>
            </div>
          </div>
        </td>
      ) : watch(`cart_items.${index}.line_status`) === 'DELETED' ? (
        <td colSpan={5} className={styles.skippedLineTd}>
          <div className={styles.skippedLineContent}>
            <span>Placeholder (manually deleted)</span>
            <div className={styles.skippedLineActionBtns}>
              <button className={styles.btn} onClick={addLineHandler}>
                <span>
                  Undo delete line
                </span>
              </button>
              <button className={styles.btn} onClick={() => skipLineHandler('SKIPPED')}>
                <span>
                  Skip line
                </span>
              </button>
            </div>
          </div>
        </td>
      ) : 
        (
        <>
              <td>
        <div className={clsx(styles.poDescriptionDiv, (isOrderLineDisabled) && styles.disabled)} data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}>
          {isDescriptionModeEnabled ? (
            <ClickAwayListener onClickAway={descriptionEditModeCloseHandler}>
              <div data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}>
                <Controller
                  name={register(`cart_items.${index}.descriptionObj`).name}
                  control={control}
                  render={({ field: { ...rest } }) => (
                    <Autocomplete
                      id={`productDescription-${actualIndex}`}
                    //   disabled={apiCallInProgress}
                      open={openDescription}
                      onClose={(e) => {
                        if (e?.keyCode === 27) {
                          setDescriptionInput('');
                        }
                        setOpenDescription(false);
                      }}
                      options={products?.length ? products : []}
                      inputValue={descriptionInput}
                      onInputChange={(_, value, reason) => {
                        if(value.length > 1 && reason === "input") setOpenDescription(true);
                        setDescriptionInput(value); setDisableBidBuyNow(true)
                      }}
                      value={watch(`cart_items.${index}.descriptionObj`)}
                      disablePortal
                      classes={{
                        root: styles.autoCompleteDesc,
                        popper: styles.autocompleteDescPanel,
                        paper: styles.autocompleteDescInnerPanel,
                        listbox: clsx(styles.listAutoComletePanel),
                        noOptions: clsx(styles.noOptionPanel, openDescription && styles.visibleNoOption),
                      }}
                      filterOptions={(options, state) => {
                        const searchTerm = state.inputValue.trim();
                        if (searchTerm.length <= 1) return [];

                        const filteredResults = searchProducts(
                          options,
                          getValidSearchData(searchTerm),
                          searchTerm,
                          userPartData,
                          true
                        );

                        setHasSearchResults(Boolean(filteredResults?.length));
                        return filteredResults;
                      }}
                      isOptionEqualToValue={(option, value) => {
                        if (!option || !value) return false;
                        return option.UI_Description === value.UI_Description;
                      }}
                      getOptionLabel={(item) => item?.UI_Description?.toUpperCase() || ""}
                      renderInput={(params) => (
                        <div ref={params.InputProps.ref}>
                          <textarea
                                  data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                            {...params.inputProps}
                            name={register(`cart_items.${index}.descriptionObj`).name}
                            type="text"
                            placeholder={getPlaceholder(index)}
                            className={clsx(
                              styles.poDescription,
                              (openDescription && descriptionInput.trim().length > 1 && hasSearchResults) && styles.poDescriptionOpen,
                              (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
                              (LargeProductsNameList?.find((largeProductsName)=> watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes(largeProductsName))) && styles.poDescriptionFirstLine1
                            )}
                            onFocus={(e) => {
                              params.inputProps.onFocus();
                              setFocusPlaceHolder(true);
                              setIsHovering(true);
                              openAddLineTab();
                              // Preselect all text on focus
                              setTimeout(() => {
                                e.target.select();
                              }, 0);
                              if(index === lineItemsToLoad-1){
                                setIsTabIndexProcessing(true);
                                handleLoadNextItems(pageIndex + 1, true, true);
                                setTimeout(() => {
                                  const descriptionInput = document.getElementById(`productDescription-${actualIndex}`);
                                  if (descriptionInput instanceof HTMLElement) {
                                    descriptionInput.focus();
                                  }
                                  setIsTabIndexProcessing(false);
                                }, 200);
                              }else if(index === 2 && pageIndex !== 0){
                                setIsTabIndexProcessing(true);
                                handleLoadNextItems(pageIndex - 1, false, true);
                                setTimeout(() => {
                                  const descriptionInput = document.getElementById(`productDescription-${actualIndex}`);
                                  if (descriptionInput instanceof HTMLElement) {
                                    descriptionInput.focus();
                                  }
                                  setIsTabIndexProcessing(false);
                                }, 200);
                              }
                            }}
                            onBlur={() => {
                              params.inputProps.onBlur();
                              setFocusPlaceHolder(false);
                              setIsHovering(false);
                              setIsDescriptionModeEnabled(false);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Tab') {
                                if(isTabIndexProcessing) return;
                                if (e.shiftKey) {
                                  const isCheckboxDisabled = !showDomesticMaterialOnly ||
                                    !(watch(`cart_items.${index}.descriptionObj`) &&
                                      Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0);

                                  // Skip focusing the domestic material checkbox if it's disabled
                                  if (isCheckboxDisabled) {
                                    if(index === 0){
                                    const uploadBillOfMaterialInput = document.getElementById('upload-bill-of-material');
                                    if (uploadBillOfMaterialInput instanceof HTMLElement) {
                                      uploadBillOfMaterialInput.focus();
                                    }
                                  }else{
                                    e.preventDefault();
                                    const qtyInput = document.querySelector(`input[id="cart_items.${actualIndex - 1}.qty"]`);
                                    if (qtyInput instanceof HTMLElement && !qtyInput.disabled) {
                                      e.preventDefault();
                                      qtyInput.focus();
                                    }else{
                                      const descriptionInput = document.getElementById(`productDescription-${actualIndex - 1}`);
                                      if (descriptionInput instanceof HTMLElement) {
                                        descriptionInput.focus();
                                      }
                                    }
                                  }
                                    return; // Let default tab behavior work
                                  }

                                  e.preventDefault();
                                  if (lineNumberRef && lineNumberRef.current) {
                                    setIsHovering(false);
                                    setIsDomesticFocused(true);
                                    lineNumberRef.current.focus();
                                  }

                                } 
                                else {
                                  if (openDescription && hasSearchResults) {
                                    e.preventDefault();
                                    const inputValue = params.inputProps.value;
                                    const activeElement = document.activeElement;
                                    
                                    // Get the aria-activedescendant attribute to find the currently focused option
                                    const activeDescendantId = activeElement.getAttribute('aria-activedescendant');
                                    
                                    if (activeDescendantId) {
                                      // Extract the option index from the ID (e.g., "productDescription-0-option-1" => 1)
                                      const optionIndexMatch = activeDescendantId.match(/option-(\d+)$/);
                                      if (optionIndexMatch && optionIndexMatch[1]) {
                                        const optionIndex = parseInt(optionIndexMatch[1]);
                                        // Get the filtered products based on the current input
                                        const options = products?.length ? products : [];
                                        const filteredOptions = searchProducts(
                                          options,
                                          getValidSearchData(inputValue),
                                          inputValue,
                                          userPartData,
                                          true
                                        );   
                                        // Get the selected item using the option index
                                        if (optionIndex >= 0 && optionIndex < filteredOptions.length) {
                                          const selectedItem = filteredOptions[optionIndex];
                                          // Update the form value with the selected item
                                          updateLineProduct(index, selectedItem);
                                          setValue(`cart_items.${index}.descriptionObj`, selectedItem);
                                          setIsDataChanged(true);
                                          saveUserActivity();
                                          setOpenDescription(false);
                                          setDescriptionInput(selectedItem?.UI_Description?.toUpperCase() || "");
                                          setIsDescriptionModeEnabled(false);
                                          setIsQtyInEditMode(true);
                                          setIsCreatePoDirty(true)
                                          if(setIsOrderLineChanges && watch('seller_name')) {
                                            setIsOrderLineChanges(true);
                                          }
                                          // Focus on the quantity field after selection
                                          setTimeout(() => {
                                            const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                                            if (qtyInput instanceof HTMLElement) {
                                              qtyInput.focus();
                                            }
                                          }, 100);
                                        }
                                      }
                                    }
                                  } 
                                }

                                setOpenDeliveryToDialog(false);
                                // setTimeout(() => {
                                //   const descriptionInput = document.querySelector('input[name="shipping_details.zip"]');
                                //   if (descriptionInput instanceof HTMLElement) {
                                //     descriptionInput.focus();
                                //   }
                                // }, 100);
                              }
                            }}
                            autoFocus // Added autoFocus to automatically focus when rendered
                          />
                        </div>
                      )}
                      {...rest}
                      onChange={(_, item) => {
                        setOpenDescription(false)
                        if (item) {
                          updateLineProduct(index, item);
                          setIsQtyInEditMode(true);
                          rest.onChange(item);
                          setIsCreatePoDirty(true)
                          setIsDataChanged(true);
                          if(setIsOrderLineChanges && watch('seller_name')) {
                            setIsOrderLineChanges(true);
                          }
                          // Focus on the quantity field after a short delay
                          setTimeout(() => {
                            const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                            if (qtyInput instanceof HTMLElement) {
                              qtyInput.focus();
                            }
                          }, 100);
                        }
                      }}
                      onBlur={(e) => {
                        if (!descriptionInput && watch(`cart_items.${index}.descriptionObj`)?.UI_Description) {
                          setIsCreatePoDirty(true);
                          setDescriptionInput('');
                          // setValue(`cart_items.${index}.descriptionObj`, {});
                          setCreatePoResultCopy(prev => {
                            const newCreatePoResultCopy = {...prev};
                            newCreatePoResultCopy[actualIndex].descriptionObj = {};
                            newCreatePoResultCopy[actualIndex].qty = '';
                            newCreatePoResultCopy[actualIndex].qty_unit = '';
                            newCreatePoResultCopy[actualIndex].buyer_line_total = 0;
                            newCreatePoResultCopy[actualIndex].seller_extended = 0;
                            newCreatePoResultCopy[actualIndex].buyer_price_per_unit = 0;
                            newCreatePoResultCopy[actualIndex].seller_price = 0;
                            return newCreatePoResultCopy;
                          });
                          // delete createPoResultCopy[actualIndex];
                          updateLineProduct(index);

                          // if (handleStoreUndoStack && isDataChanged) {
                          //   handleStoreUndoStack({ ...undoStackObject, currentValue: '', from: "line", actualIndex: actualIndex, index: index });
                          //   setIsDataChanged(false);
                          // }
                        } else{
                          if (handleStoreUndoStack && isDataChanged) {
                            handleStoreUndoStack({ ...undoStackObject, currentValue: watch(`cart_items.${index}.descriptionObj`), from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex });
                            setIsDataChanged(false);
                            if(setIsOrderLineChanges && watch('seller_name')) {
                              setIsOrderLineChanges(true);
                            }
                          }
                        }
                        
                        setUndoStackObject({});
                        setOpenDescription(false)
                        rest.onBlur(e);
                        saveUserActivity();
                      }}
                      renderOption={(props, option) => (
                        <span key={option.id} {...props} className={clsx(styles.productDescription, LargeProductsNameList?.find((largeProductsName)=> option.UI_Description?.split("\n")[0]?.includes(largeProductsName)) && styles.miscellaneousText)}>
                          {display(option)}
                        </span>
                      )}
                    />
                  )}
                />
              </div>
            </ClickAwayListener>
          )
          :
            <p className={styles.descriptionModeDisabled}>
              <textarea
                data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                type="text"
                name={register(`cart_items.${index}.descriptionObj`).name}
                id={`productDescription-${actualIndex}`}
                value={watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.toUpperCase() || ''}
                placeholder={getPlaceholder(index)}
                className={clsx(styles.poDescription, (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
                (LargeProductsNameList?.find((largeProductsName)=> watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes(largeProductsName))) && styles.poDescriptionFirstLine1
              )}
                onClick={() => {
                  setIsDescriptionModeEnabled(true);
                }}
                onFocus={() => {
                  setIsHovering(true);
                  setIsDescriptionModeEnabled(true)
                  openAddLineTab()
                  setUndoStackObject({name: `descriptionObj`, value: watch(`cart_items.${index}.descriptionObj`), id: `cart_items.${index}.descriptionObj`});
                }}
                onBlur={(e) => {
                  setIsHovering(false);
                }}
                onKeyDown={(e) => {
                  if(e.key === 'Tab' && e.shiftKey){
                    const isCheckboxDisabled = !showDomesticMaterialOnly || 
                      !(watch(`cart_items.${index}.descriptionObj`) && 
                        Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0);
                    
                    // Skip focusing the domestic material checkbox if it's disabled
                    if (isCheckboxDisabled) {
                      return; // Let default tab behavior work
                    }
                    
                    e.preventDefault();
                    if (lineNumberRef && lineNumberRef.current) {
                      setIsHovering(false);
                      setIsDomesticFocused(true);
                      lineNumberRef.current.focus();
                    }
                    
                    setOpenDeliveryToDialog(false);
                    // setTimeout(() => {
                    //   const descriptionInput = document.querySelector('input[name="shipping_details.zip"]');
                    //   if (descriptionInput instanceof HTMLElement) {
                    //     descriptionInput.focus();
                    //   }
                    // }, 100);
                  }
                }}
                disabled={!watch('isEdit') || isOrderLineDisabled || (!isHeaderDetailsConfirmed && cameFromSavedBom) || (location.pathname === routes.orderManagementPage && watch(`cart_items.${index}.id`)) || isStateZipValChange}
                readOnly
              />
            </p>
          }
            {(watch(`cart_items.${index}.descriptionObj`)?.UI_Description && !isSeller) &&
              <Tooltip
                title={
                  sameTagErrorMsg && (
                    <span
                      dangerouslySetInnerHTML={{ __html: sameTagErrorMsg }}
                    ></span>
                  )
                }
                arrow
                placement={"bottom-end"}
                disableInteractive
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 200 }}
                classes={{
                  tooltip: "partNumberTooltip",
                }}
              >
                <div className={styles.partNumberFiled}
                      data-hover-video-id="part-number-po">
                  <InputWrapper>
                    <CustomTextField
                      dataHoverVideoId="part-number-po"
                      type='text'
                      register={register(`cart_items.${index}.product_tag`)}
                      disabled={(!watch('isEdit') || (location.pathname === routes.orderManagementPage && watch(`cart_items.${index}.id`)) || isStateZipValChange)}
                      placeholder="Add YOUR PART #"
                      className={clsx({ [styles.errorInput]: errors?.cart_items?.[index]?.product_tag?.message })}
                      value={watch(`cart_items.${index}.product_tag`) ?? ""}
                      onmouseenter={() => {
                        setIsHovering(true);
                      }}
                      onFocus={() => {
                        setIsHovering(true);
                        setIsDescriptionModeEnabled(false);
                        setUndoStackObject({name: `product_tag`, value: watch(`cart_items.${index}.product_tag`), id: `cart_items.${index}.product_tag`});
                      }}
                      onBlur={(e) => {
                        register(`cart_items.${index}.product_tag`).onBlur(e);
                        saveUserActivity();
                        setIsHovering(false);
                        if(handleStoreUndoStack && isDataChanged){
                          handleStoreUndoStack({...undoStackObject, currentValue: e.target.value, from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex});
                          setIsDataChanged(false);
                        }
                        setUndoStackObject({});
                        // setIsQtyInEditMode(true);
                      }}
                      onChange={(e) => {
                        setIsDataChanged(true);
                        setIsCreatePoDirty(true)
                        if(setIsOrderLineChanges && watch('seller_name')) {
                          setIsOrderLineChanges(true);
                        }
                      }}
                      onKeyUp={(e) => {
                        setValue(
                          `cart_items.${index}.product_tag`,
                          e.target.value
                        );
                      }}
                    />
                  </InputWrapper>
                </div>
              </Tooltip>
            }

        </div>
      </td>
      <td>
        <div className={styles.poQtyContainer} >
          <div className={styles.poQty}
          data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
          >
            <div>
              {isQtyInEditMode ? (
                <ClickAwayListener  onClickAway={qtyEditModeCloseHandler}>
                  <Tooltip
                    title={(formErrors[actualIndex]?.qty && watch(`cart_items.${index}.descriptionObj`)?.UI_Description) &&
                    `Quantity can only be multiples of ${getValUsingUnitKey(watch(`cart_items.${index}.descriptionObj`), watch(`cart_items.${index}.qty_unit`), orderIncrementPrefix)}`
                  }
                    arrow
                    placement={"bottom-start"}
                    disableInteractive
                    TransitionComponent={Fade}
                    TransitionProps={{ timeout: 200 }}
                    classes={{
                      tooltip: "inputQtyTooltip",
                    }}
                  >
                    <div>
                      <InputWrapper>
                        <CustomTextField 
                          dataHoverVideoId={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                          type='text'
                          register={register(`cart_items.${index}.qty`)}
                          disabled={!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) || isStateZipValChange}
                          value={watch(`cart_items.${index}.qty`) ?? ""}
                          onFocus={() => {
                            setIsHovering(true);
                            setCheckAfterFocus(true);
                            openAddLineTab();
                            setUndoStackObject({name: `qty`, value: watch(`cart_items.${index}.qty`), id: `cart_items.${index}.qty`});
                          }}
                          className={clsx(formErrors[actualIndex]?.qty && styles.errorQty)}
                          onChange={(e) => {
                            quantityChangeHandler(e);
                            setIsCreatePoDirty(true);
                            setIsDataChanged(true);
                            if(setIsOrderLineChanges && watch('seller_name')) {
                              setIsOrderLineChanges(true);
                            }
                          }}
                          onBlur={(e) => {
                            register(`cart_items.${index}.qty`).onBlur(e);
                            qtyEditModeCloseHandler();
                            saveUserActivity();
                            setIsHovering(false);
                            if(handleStoreUndoStack && isDataChanged){
                              handleStoreUndoStack({...undoStackObject, currentValue: e.target.value, from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex});
                              setIsDataChanged(false);
                            }
                            setUndoStackObject({});
                          }}
                          errorInput={formErrors[actualIndex]?.qty}
                          mode={(watch(`cart_items.${index}.qty_unit`)?.toLowerCase() === priceUnits.pc) ? "wholeNumber" : "number"}
                          id={`cart_items.${index}.qty`}
                          onKeyDown={(e) => {
                            if(e.key === 'Tab' && !e.shiftKey){
                              e.preventDefault();
                              const descriptionInput = document.getElementById(`productDescription-${actualIndex + 1}`);
                              if (descriptionInput instanceof HTMLElement) {
                                descriptionInput.focus();
                              }
                            }
                          }}
                        />
                      </InputWrapper>
                    </div>
                  </Tooltip>
                </ClickAwayListener>
              ) : (
                  <p
                    className={styles.poQtyValue}
                  >
                    <input type="text" className={styles.poQtyValue}
                     value={(watch(`cart_items.${index}.qty`) && watch(`cart_items.${index}.qty_unit`)?.toLowerCase() !==  priceUnits.pc) ? removeDecimalZero(formatToTwoDecimalPlaces(watch(`cart_items.${index}.qty`))) : removeDecimalZero(watch(`cart_items.${index}.qty`))}
                      onClick={() => {
                        setIsQtyInEditMode(true);
                        setTimeout(() => {
                          const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                          if (qtyInput instanceof HTMLElement) {
                            qtyInput.focus();
                          }
                        }, 100);
                      }}
                      onFocus={() => {
                        setIsHovering(true);
                        setIsQtyInEditMode(true);
                        setCheckAfterFocus(true);
                        setTimeout(() => {
                          const qtyInput = document.querySelector(`input[name="cart_items.${index}.qty"]`);
                          if (qtyInput instanceof HTMLElement) {
                            qtyInput.focus();
                          }
                        }, 100);
                      }}
                      onBlur={() => {
                        setIsHovering(false);
                      }}
                      id={`cart_items.${index}.qty`}
                      readOnly
                      disabled= {!(watch('isEdit') && watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) || isStateZipValChange}
                      data-hover-video-id={ orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                       />
                  </p>
                )}
            </div>
            {(watch(`cart_items.${index}.qty_unit`) && watch('isEdit') && !isStateZipValChange) ?
              <span className={styles.selectUom}>
              <CustomMenu
                name={register(`cart_items.${index}.qty_unit`).name}
                control={control}
                // disabled={apiCallInProgress}
                onChange={(e) => {
                  console.log("e.target.value @>>>>>>>", e.target.value);
                  if(e.target.value === priceUnits.pc){
                    setValue(`cart_items.${index}.qty`, watch(`cart_items.${index}.qty`).replace(/(\d+)\.([5-9])\d*/g, (_, whole, decimal) => String(Number(whole) + 1)).replace(/(\d+)\.([0-4])\d*/g, (_, whole) => whole));
                  }
                  setIsCreatePoDirty(true);
                  if(e.target.value === createPoResultCopy[actualIndex].qty_unit) return;
                  if(qtyUnitDebounceTimer) clearTimeout(qtyUnitDebounceTimer);
                  setQtyUnitDebounceTimer(setTimeout(() => {
                    createPoResultCopy[actualIndex].qty_unit = e.target.value?.trim()?.toLowerCase();
                    calculateExtendedPrice(actualIndex);
                  }, 400));
                    // handlePriceIntegration(actualIndex);
                  // updateValue(index);
                  if(e.target.value !== createPoResultCopy[actualIndex].qty_unit){
                    saveUserActivity();
                  }
                  if(handleStoreUndoStack){
                    handleStoreUndoStack({name: `qty_unit`, value: createPoResultCopy[actualIndex].qty_unit, id: `cart_items.${index}.qty_unit`, currentValue: e.target.value, from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex});
                  }
                  setUndoStackObject({});
                  if(setIsOrderLineChanges && watch('seller_name')) {
                    setIsOrderLineChanges(true);
                  }
                }}
                items={
                  getValues(`cart_items.${index}.qty_um`)?.map((x) => ({ title: (x.toLowerCase() === priceUnits.pc) ? 'PC' : x, value: x.toLowerCase() })) ?? []
                }
                className={clsx(styles.uomDrodown,'qtyUnitDropdown')}
                MenuProps={{
                  classes: {
                    paper: styles.selectUomPaper,
                  },
                  id: `qty-unit-menu-${index}` // Add index to make ID unique
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Tab' && document.activeElement?.closest(`#qty-unit-menu-${index}`)) { // Update selector to match new ID
                    const value = document.activeElement.getAttribute('data-value');
                    setValue(`cart_items.${index}.qty_unit`, value?.trim()?.toLowerCase());
                    if(value === priceUnits.pc){
                      setValue(`cart_items.${index}.qty`, watch(`cart_items.${index}.qty`).replace(/(\d+)\.([5-9])\d*/g, (_, whole, decimal) => String(Number(whole) + 1)).replace(/(\d+)\.([0-4])\d*/g, (_, whole) => whole));
                    }
                    setIsCreatePoDirty(true);

                    if (value === watch(`cart_items.${index}.qty_unit`)) return;
                    if (qtyUnitDebounceTimer) clearTimeout(qtyUnitDebounceTimer);
                    setQtyUnitDebounceTimer(setTimeout(() => {
                      createPoResultCopy[actualIndex].qty_unit = value;
                      calculateExtendedPrice(actualIndex);
                    }, 400));
                    // handlePriceIntegration(actualIndex);
                    // updateValue(index);
                    saveUserActivity();
                    if(setIsOrderLineChanges && watch('seller_name')) {
                      setIsOrderLineChanges(true);
                    }
                  }
                }}
              />
            </span>
            :
            <span className={styles.selectUom}>
              <span className={styles.uomDrodown}>
                {watch(`cart_items.${index}.qty_unit`)}
              </span>
            </span>
            }
          </div>
          {
            watch(`cart_items.${index}.line_status`) && (
                <div className={styles.lineStatusActionBtns}>
                  {
                    watch(`cart_items.${index}.line_status`) === 'APPROVED' && (
                      <div className={styles.actionBtns}>
                        <button className={styles.btn} onClick={() => skipLineHandler('SKIPPED')} disabled={!watch('isEdit')}>
                          <span>
                            Skip line
                          </span>
                        </button>
                        <button className={styles.btn} onClick={() => skipLineHandler('DELETED')} disabled={!watch('isEdit')}>
                          <span>
                            Delete line
                          </span>
                        </button>
                      </div>
                    )
                  }
                </div>
            )
          }
          {(!watch(`cart_items.${index}.id`) && isEditingPo && watch(`cart_items.${index}.descriptionObj`)?.UI_Description && watch('seller_name')) && (
            <div className={styles.lineStatusActionBtns}>
              <div className={styles.actionBtns}>
                  <ClickAwayListener onClickAway={() => setIsCalendarOpen(false)}>
                <div className={styles.deliverNewLineContainer}>
                  <button
                    type="button"
                    className={styles.btn}
                    onClick={handleOpenCalendar}
                    onFocus={handleOpenCalendar}
                  >
                    Deliver New Line by:
                   <span className={styles.arrowIcon}><DownArrowIcon /></span>
                  </button>
                  
                  {selectedDate && (
                    <div className={styles.selectedDateDisplay}>
                      <span className={styles.selectedDateText}>
                        {selectedDate.format('ddd, MMM DD, YYYY')}
                      </span>
                    </div>
                  )}
                  
                  {isCalendarOpen && (
                      <div className={styles.calendarContainer}>
                        <Calendar
                          value={selectedDate?.format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit) || ''}
                          setValue={setValue}
                          isCalendarOpen={isCalendarOpen}
                          setIsCalendarOpen={setIsCalendarOpen}
                          disableDeliveryDate={disableDeliveryDate}
                          handleOpenCalendar={handleOpenCalendar}
                          saveUserActivity={() => {}}
                          saveBomHeaderDetails={() => {}}
                          onDateSelect={handleDateSelect}
                          allowedDates={deliveryAllowedDates}
                        />
                      </div>
                  )}
                </div>
                    </ClickAwayListener>
              </div>
            </div>
          )}
        </div>
      </td>
      <td>
        <div className={styles.poPerUm}>
          <PriceHistoryDropdown
            lineHistory={createPoResultCopy?.[actualIndex]?.line_history}
            currentPrice={watch(`cart_items.${index}.buyer_price_per_unit`) ?? 0}
            dollerPerUmFormatter={dollerPerUmFormatter}
            index={index}
            watch={watch}
            register={register}
            control={control}
            setValue={setValue}
            getValues={getValues}
            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
            setIsCreatePoDirty={setIsCreatePoDirty}
            saveUserActivity={saveUserActivity}
            actualIndex={actualIndex}
            priceUnits={priceUnits}
            isStateZipValChange={isStateZipValChange}
            setIsOrderLineChanges={setIsOrderLineChanges}
          />
        </div>
      </td>
      <td>
        <div className={styles.extendedValue}>
          {!!(watch(`cart_items.${index}.buyer_line_total`)) &&
            formatToTwoDecimalPlaces(watch(`cart_items.${index}.buyer_line_total`) ?? 0)
          }
        </div>
      </td>
        </>
        )
      }
  
    </tr>
  );
};

export default CreatePoTile;

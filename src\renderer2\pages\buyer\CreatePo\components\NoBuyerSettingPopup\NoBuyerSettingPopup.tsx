import { ClickAwayListener, Dialog } from '@mui/material'
import React from 'react'
import styles from './NoBuyerSettingPopup.module.scss'

const NoBuyerSettingPopup = ({ containerRef, openNoBuyerSettingPopup, setOpenNoBuyerSettingPopup, handleGoToSettings }: { containerRef: React.RefObject<HTMLDivElement>, openNoBuyerSettingPopup: boolean, setOpenNoBuyerSettingPopup: (open: boolean) => void, handleGoToSettings: () => void }) => {
    return (
        <Dialog
            open={openNoBuyerSettingPopup}
            onClose={() => setOpenNoBuyerSettingPopup(false)}
            transitionDuration={100}
            disableScrollLock={true}
            container={containerRef?.current}
            
            style={{
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backdropFilter: 'blur(7px)',
                background: 'rgba(0, 0, 0, 1)', // Fully opaque black
                WebkitBackdropFilter: 'blur(7px)',
                // backgroundColor: 'rgba(0, 0, 0, 0)',
                border: '1px solid transparent',
                zIndex: 999999
            }}
            PaperProps={{
                style: {
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    margin: 0,
                    width: '100%',
                    maxWidth: '980px',
                    minWidth: '761px',
                    borderRadius: '16px',
                    boxShadow: '0 0 67.4px 4px #000',
                    backgroundColor: 'black',
                }
            }}
            hideBackdrop
        >
            <ClickAwayListener onClickAway={() => setOpenNoBuyerSettingPopup(false)}>

            <div className={styles.modal}>
                <p onClick={() => setOpenNoBuyerSettingPopup(false)}>X</p>
                <h1>almost there!</h1>
                <p>
                Before completing checkout, please finish filling out your Settings. There is information required to process this order. 

This order is saved here in Purchasing so you can checkout immediately upon completion.
                </p>
                <button onClick={handleGoToSettings}>Go to Settings</button>
            </div>

            </ClickAwayListener>
        </Dialog>
    )
}

export default NoBuyerSettingPopup
